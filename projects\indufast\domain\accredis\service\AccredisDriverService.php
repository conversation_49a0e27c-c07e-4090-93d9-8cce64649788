<?php
  namespace domain\accredis\service;

  use User;

  class AccredisDriverService {

    /**
     * @return User[]
     */
    public static function getAll(): array {
      $drivers = [];

      foreach (User::find_all("JOIN user_profile AS up ON up.user_id = user.id AND up.code = 'accredis-driver-name' AND up.value IS NOT NULL") as $user) {
        $name = \UserProfile::find_by(['user_id' => $user->id, 'code' => 'accredis-driver-name'])->value;
        $drivers[$name] = [
          'user' => $user,
          'accredis-driver-name' => $name,
        ];
      }

      return $drivers;
    }
  }