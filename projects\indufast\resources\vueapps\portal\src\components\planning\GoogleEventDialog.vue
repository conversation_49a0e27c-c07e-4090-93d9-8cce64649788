<script setup>
defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  event: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue']);

const closeDialog = () => {
  emit('update:modelValue', false);
};
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    width="600px"
    scrollable
    @update:model-value="closeDialog"
  >
    <v-card>
      <v-toolbar color="primary">
        <v-toolbar-title>
          {{ event.title }}
        </v-toolbar-title>
        <v-toolbar-items>
          <v-btn
            icon="mdi-close"
            @click="closeDialog"
          />
        </v-toolbar-items>
      </v-toolbar>
      <v-card-text class="pa-2">
        <v-list
          density="compact"
          class="pt-0"
        >
          <v-list-item
            prepend-icon="mdi-clock-outline"
            slim
          >
            <template v-if="event.start !== event.end">
              {{ $filters.formatTime(event.start) }} - {{ $filters.formatTime(event.end) }}
            </template>
            <template v-else>
              Hele dag
            </template>
          </v-list-item>
          <v-list-item
            v-if="event.location"
            prepend-icon="mdi-map-marker"
            slim
          >
            <a
              :href="'https://maps.google.com/?q=' + encodeURIComponent(event.location)"
              target="_blank"
            >
              {{ event.location }}
            </a>
          </v-list-item>
          <v-list-item
            v-if="event.description"
            prepend-icon="mdi-note"
            slim
          >
            <p
              class="markup"
              v-html="event.description"
            />
          </v-list-item>
          <v-list-item
            v-if="event.people?.length"
            prepend-icon="mdi-account-group"
            slim
          >
            <div class="d-flex flex-wrap ga-2">
              <v-chip
                v-for="person in event.people"
                :key="person"
                size="small"
                variant="outlined"
              >
                {{ person }}
              </v-chip>
            </div>
          </v-list-item>
        </v-list>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.markup {
  white-space: pre-line;
}
</style>
