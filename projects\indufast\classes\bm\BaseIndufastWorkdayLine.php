<?php
class BaseIndufastWorkdayLine extends AppModel {

  const DB_NAME = '';
  const TABLE_NAME = 'indufast_workday_line';
  const OM_CLASS_NAME = 'IndufastWorkdayLine';
  const columns = ['id', 'workday_id', 'external_id', 'start', 'end', 'start_address', 'end_address', 'distance', 'type', 'vehicle', 'void', 'remark', 'insertTS', 'updateTS'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'workday_id'                  => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'external_id'                 => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'start'                       => ['type' => 'time', 'length' => '', 'null' => false],
    'end'                         => ['type' => 'time', 'length' => '', 'null' => false],
    'start_address'               => ['type' => 'varchar', 'length' => '256', 'null' => true],
    'end_address'                 => ['type' => 'varchar', 'length' => '256', 'null' => true],
    'distance'                    => ['type' => 'decimal', 'length' => '10,1', 'null' => true],
    'type'                        => ['type' => 'enum', 'length' => '6', 'null' => true, 'enums' => ['travel-to','travel-from','hours','leave','special-leave','sick']],
    'vehicle'                     => ['type' => 'varchar', 'length' => '256', 'null' => true],
    'void'                        => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'remark'                      => ['type' => 'text', 'length' => '', 'null' => true],
    'insertTS'                    => ['type' => 'datetime', 'length' => '', 'null' => false],
    'updateTS'                    => ['type' => 'datetime', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $workday_id, $external_id, $start, $end, $start_address, $end_address, $distance, $type, $vehicle, $void, $remark, $insertTS, $updateTS;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkdayLine[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkdayLine[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return IndufastWorkdayLine[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkdayLine
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkdayLine
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}