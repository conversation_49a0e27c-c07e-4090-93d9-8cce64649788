<script setup>
import {employeeAvailability, employeeTypes, industryTypes} from "@/helpers/constants.js";
import {computed} from "vue";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  employee: {
    type: Object,
    default: () => ({})
  },
  updateInternalAvailability : {
    type: Function,
    default: () => {}
  }
});
const emit = defineEmits(['update:modelValue']);
const closeDialog = () => {
  emit('update:modelValue', false);
}

const isLoading = computed(() => props.employee && (!props.employee._events || !props.employee._availability));
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    width="900px"
    scrollable
    @update:model-value="emit('update:modelValue', $event)"
  >
    <v-card>
      <v-toolbar color="primary">
        <v-toolbar-title>
          {{ employee.name }} {{ employee.rank }}{{ employee.rank_number }}
          <v-progress-circular
            v-if="isLoading"
            indeterminate
            color="primary"
          />
        </v-toolbar-title>
        <v-toolbar-items>
          <v-btn
            icon
            @click="closeDialog"
          >
            <v-icon>
              mdi-close
            </v-icon>
          </v-btn>
        </v-toolbar-items>
      </v-toolbar>
      <v-card-text class="pa-2">
        <v-list
          density="compact"
        >
          <v-list-item
            slim
            prepend-icon="mdi-email"
          >
            <a :href="`mailto:${employee.email}`">
              {{ employee.email }}
            </a>
          </v-list-item>

          <v-list-item
            v-if="employee.extra_email"
            slim
            prepend-icon="mdi-email-plus"
          >
            <a :href="`mailto:${employee.extra_email}`">
              {{ employee.extra_email }}
            </a>
          </v-list-item>

          <v-list-item
            slim
            prepend-icon="mdi-account-box-multiple"
          >
            {{ employeeTypes.find(type => type.value === employee.type)?.title }}
          </v-list-item>

          <v-list-item
            slim
            prepend-icon="mdi-calendar-remove"
          >
            {{ employeeAvailability.find(type => type.value === updateInternalAvailability(employee))?.title }}
          </v-list-item>

          <v-list-item
            v-if="employee.industries"
            slim
            prepend-icon="mdi-factory"
          >
            <v-chip
              v-for="(item, index) in employee.industries"
              :key="index"
              :color="industryTypes.find(type => type.value === item)?.color"
              variant="flat"
              class="mr-2"
              :prepend-icon="industryTypes.find(type => type.value === item)?.icon"
            >
              {{ industryTypes.find(type => type.value === item)?.title }}
            </v-chip>
          </v-list-item>
        </v-list>


        <template v-if="employee._events && !employee._events.length">
          <v-divider />
          <v-card-text class="text-center">
            <v-icon
              size="x-large"
              color="grey lighten-1"
            >
              mdi-calendar-remove
            </v-icon>
            <div class="mt-2">
              Geen afspraken gevonden
            </div>
          </v-card-text>
        </template>

        <template
          v-for="event in employee._events"
          :key="event.id"
        >
          <v-divider />
          <v-card-title class="d-flex align-center ga-2">
            <v-icon
              v-if="event.visibility === 'private'"
              size="x-small"
            >
              mdi-eye-off
            </v-icon>
            {{ event.summary ?? 'Bezet' }}
          </v-card-title>
          <v-list
            density="compact"
            class="pt-0"
          >
            <v-list-item
              prepend-icon="mdi-clock-outline"
              slim
            >
              <template v-if="event.start.date">
                Hele dag
              </template>
              <template v-else>
                {{ $filters.formatTime(event.start.dateTime) }} - {{ $filters.formatTime(event.end.dateTime) }}
              </template>
            </v-list-item>

            <v-list-item
              v-if="event.location"
              prepend-icon="mdi-map-marker"
              slim
            >
              <a
                :href="`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(event.location)}`"
                target="_blank"
                rel="noopener noreferrer"
              >
                {{ event.location }}
              </a>
            </v-list-item>

            <v-list-item
              v-if="event.description"
              class="mb-2 description-content"
              prepend-icon="mdi-note"
              slim
            >
              <div
                class=""
                v-html="event.description"
              />
            </v-list-item>
          </v-list>
        </template>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.description-content {
  white-space: pre-line;
}
</style>
