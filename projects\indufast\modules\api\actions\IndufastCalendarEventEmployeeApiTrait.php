<?php

  use classes\ApiResponse;

  trait IndufastCalendarEventEmployeeApiTrait {

    public function executeEventEmployeeCreate(): void {
      $eventEmployee = new IndufastCalendarEventEmployee();
      $eventEmployee->fill((array)$this->data)->validate();

      if ($eventEmployee->event()->type != 'work') {
        ApiResponse::sendResponseError('Invalid event type');
      }

      try {
        $eventEmployee->save();
        ApiResponse::sendResponseOK('Employee saved', $eventEmployee);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.');
      }
    }

    public function executeEventEmployeeDelete(): void {
      if (!$eventEmployee = IndufastCalendarEventEmployee::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Employee not found');
      }

      try {
        $eventEmployee->destroy();
        ApiResponse::sendResponseOK('Employee deleted', $eventEmployee);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while deleting.');
      }
    }

  }