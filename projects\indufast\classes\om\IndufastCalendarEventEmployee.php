<?php

  AppModel::loadModelClass('IndufastCalendarEventEmployeeModel');

  class IndufastCalendarEventEmployee extends IndufastCalendarEventEmployeeModel {

    use PropertyCastTrait {
      castProperties as castPropertiesTrait;
    }
    use ModelFillTrait;
    use ModelValidateTrait;

    protected array $fillable = [
      'calendar_event_id' => 'required|integer|exists:indufast_calendar_event,id',
      'employee_id'       => 'required|integer|exists:indufast_employee,id',
    ];

    const CAST_PROPERTIES = [
      'id'                => 'int',
      'calendar_event_id' => 'int',
      'employee_id'       => 'int',
      'from_db'           => 'hidden',
    ];

    // Additional properties (assuming database will be updated)
    public $conflict;

    public IndufastEmployee $employee;
    public IndufastCalendarEvent $calendar_event;

    public function castProperties(): void {
      $this->employee();
      $this->castPropertiesTrait();

      $this->has_conflicts = (bool)$this->has_conflicts;
    }

    /**
     * Get parsed conflict details
     * @return array|null
     */
    public function getConflictDetails(): ?array {
      if (empty($this->conflict)) {
        return null;
      }

      $decoded = json_decode($this->conflict, true);
      return is_array($decoded) ? $decoded : null;
    }

    /**
     * Check if has specific type of conflict
     * @param string $type
     * @return bool
     */
    public function hasConflictType(string $type): bool {
      $conflicts = $this->getConflictDetails();
      if (!$conflicts) return false;

      return array_any($conflicts, fn($conflict) => $conflict['type'] === $type);
    }

    /**
     * Get conflicts by severity
     * @param string $severity
     * @return array
     */
    public function getConflictsBySeverity(string $severity): array {
      $conflicts = $this->getConflictDetails();
      if (!$conflicts) return [];

      return array_filter($conflicts, fn($conflict) => $conflict['severity'] === $severity);
    }

    public function employee(): IndufastEmployee {
      if ($employee = IndufastEmployee::find_by_id($this->employee_id)) {
        return $this->employee = $employee;
      }

      throw new Exception("Employee not found");
    }

    public function event(): IndufastCalendarEvent {
      if ($calendar_event = IndufastCalendarEvent::find_by_id($this->calendar_event_id)) {
        return $this->calendar_event = $calendar_event;
      }

      throw new Exception("Event not found");
    }
  }