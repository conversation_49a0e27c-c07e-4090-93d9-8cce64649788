import { defineStore } from 'pinia'

export const useAuthenticationStore = defineStore('authentication', {
  state: () => ({
    isLoggedIn: false,
    currentUser: null,
    rail: true,
  }),
  getters: {
  },
  actions: {
    setLoginData(user, state) {
      this.setCurrentUser(user)
      this.setIsLoggedIn(state);
    },
    setIsLoggedIn(loggedInState) {
      this.isLoggedIn = loggedInState;
    },
    setCurrentUser(user) {
      this.currentUser = user;
    },
    logOut() {
      this.setCurrentUser(null);
      this.setIsLoggedIn(false);
    },
    getCurrentUserName() {
      return this.currentUser ? this.currentUser.firstName : '';
    },
    getCurrentUserFullName() {
      return this.currentUser ? [this.currentUser.firstName, this.currentUser.insertion, this.currentUser.lastName].join(' ') : '';
    }
  },
  persist: {storage: localStorage}, // store in localstorage
})
