<?php

  namespace classes;
  trait TimesTrait {

    private function duration(string $start, string $end, int $offsetMinutes = 0): string {
      $start = new \DateTime($start . " + $offsetMinutes minutes");
      $end = new \DateTime($end);
      $interval = $start->diff($end);
      return ($start < $end) ? $interval->format('%H:%I:%S') : '00:00:00';
    }

    function addTimes(array $times): string {
      $totalSeconds = 0;

      foreach ($times as $time) {
        [$hours, $minutes, $seconds] = array_map('intval', explode(':', $time));
        $totalSeconds += ($hours * 3600) + ($minutes * 60) + $seconds;
      }

      $hours = floor($totalSeconds / 3600);
      $minutes = floor(($totalSeconds % 3600) / 60);
      $seconds = $totalSeconds % 60;

      return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }
  }