<script setup>
import {ref, onMounted} from "vue";
import {useTitle} from 'vue-page-title';
import { useRouter } from 'vue-router';
import createApiService from '@/services/api';

const {setTitle} = useTitle();
const {title} = useTitle();
const router = useRouter();
const api = createApiService(router);
const currentYear = new Date().getFullYear();
const currentMonth = new Date().getMonth();

let users = ref([]);

onMounted(() => {
  console.log("Mounted");
  setTitle('Accredis - Overzicht');
  api
    .get("getUsers")
    .then((response) => {
      console.log(response.data);
      users.value = response.data.data;
    })
    .catch((error) => {
      console.log(error);
    });
});
</script>

<template>
  <v-card>
    <v-card-title>{{ title }}</v-card-title>
    <v-card-text>
      <v-table>
        <tr>
          <th>Naam</th>
        </tr>
        <tr v-for="user in users" :key="user.id">
          <td>
            <RouterLink
              :to="{ name: 'accredis-details', params: { user: user.id, year: currentYear, month: currentMonth } }"
            >
              {{ user.name }}
            </RouterLink>
          </td>
        </tr>
      </v-table>
    </v-card-text>
  </v-card>
</template>
