<?php

  use classes\ApiResponse;
  use Somnambulist\Components\Validation\Factory;
  use Somnambulist\Components\Validation\Validation;

  trait ApiRequestValidateTrait {

    protected function validateRequest($data, $rules): Validation {
      $validation = (new Factory)->make($data, $rules);
      $validation->validate();

      if ($validation->fails()) {
        ApiResponse::sendResponseError('Invalid request', $validation->errors()->all());
      }

      return $validation;
    }

  }
