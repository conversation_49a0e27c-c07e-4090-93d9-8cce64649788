<?php

  namespace domain\project\service;

  use DBConn;
  use IndufastCalendarEvent;
  use IndufastCalendarEventEmployee;
  use IndufastEmployee;
  use IndufastProject;

  class ProjectUpdater {

    /**
     * @throws \GsdDbException
     * @throws \GsdException
     * @throws \Exception
     */
    public static function updateProjectValidity(): void {
      // We only want to check for one month ahead
      $today = date('Y-m-d');
      $oneMonthAhead = date('Y-m-d', strtotime('+1 month'));

      $employees = IndufastEmployee::find_all();
      $eventsByEmployee = IndufastEmployee::getCalendarEventsForEmployees($employees, $today, $oneMonthAhead);
      $indufastEvents = IndufastCalendarEvent::find_all("WHERE start >= '$today 0:00:00' AND start < '$oneMonthAhead 0:00:00' ORDER BY start");

      foreach ($indufastEvents as $event) {
        $event->dayPart();

        $eventEmployees = $event->employees();
        if (empty($eventEmployees)) continue;

        foreach ($eventEmployees as $eventEmployee) {
          $employee = array_find($employees, fn($e) => $e->id === $eventEmployee->employee_id);
          if (!$employee) {
            logToFile(__FUNCTION__, "Employee not found: " . var_export($eventEmployee, 1));
            continue;
          }

          $date = date('Y-m-d', strtotime($event->start));
          $eventsForEmployee = $eventsByEmployee[$employee->id] ?? [];
          $availability = $employee->calculateAvailabilityFromEvents($eventsForEmployee, $date, false, $event);

          $eventEmployee->has_conflicts = $employee->hasConflict($event, $availability) ? 1 : 0;
          $eventEmployee->from_db = true;
          $eventEmployee->save();
        }
      }
    }

    public static function cleanupPastEvents(): void {
      $event = IndufastCalendarEvent::getTablename();
      $eventEmployee = IndufastCalendarEventEmployee::getTablename();

      // update all events from before yesterday
      $yesterday = date('Y-m-d 0:00:00', strtotime('-1 day'));
      $query = <<<SQL
        UPDATE $eventEmployee 
        SET has_conflicts = 0 
        WHERE calendar_event_id IN (
          SELECT id FROM $event 
          WHERE start < '$yesterday'
        )
      SQL;
      DBConn::db_link()->query($query);
    }
  }