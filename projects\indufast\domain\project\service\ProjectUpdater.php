<?php

  namespace domain\project\service;

  use DBConn;
  use IndufastCalendarEvent;
  use IndufastCalendarEventEmployee;
  use IndufastEmployee;
  use IndufastProject;

  class ProjectUpdater {

    /**
     * @throws \GsdDbException
     * @throws \GsdException
     * @throws \Exception
     */
    public static function updateProjectValidity(): void {
      // We only want to check for one month ahead
      $today = date('Y-m-d');
      $oneMonthAhead = date('Y-m-d', strtotime('+1 month'));

      $employees = IndufastEmployee::find_all();
      $eventsByEmployee = IndufastEmployee::getCalendarEventsForEmployees($employees, $today, $oneMonthAhead);
      $indufastEvents = IndufastCalendarEvent::find_all("WHERE start >= '$today 0:00:00' AND start < '$oneMonthAhead 0:00:00' ORDER BY start");

      foreach ($indufastEvents as $event) {
        $event->dayPart();

        $eventEmployees = $event->employees();
        if (empty($eventEmployees)) continue;

        foreach ($eventEmployees as $eventEmployee) {
          $employee = array_find($employees, fn($e) => $e->id === $eventEmployee->employee_id);
          if (!$employee) {
            logToFile(__FUNCTION__, "Employee not found: " . var_export($eventEmployee, 1));
            continue;
          }

          $date = date('Y-m-d', strtotime($event->start));
          $eventsForEmployee = $eventsByEmployee[$employee->id] ?? [];
          $availability = $employee->calculateAvailabilityFromEvents($eventsForEmployee, $date, false, $event);

          // Get detailed conflict information
          $conflictDetails = self::detectConflicts($employee, $event, $availability, $indufastEvents);

          $eventEmployee->has_conflicts = !empty($conflictDetails) ? 1 : 0;
          $eventEmployee->conflict = !empty($conflictDetails) ? json_encode($conflictDetails) : null;
          $eventEmployee->from_db = true;
          $eventEmployee->save();
        }
      }
    }

    /**
     * Detect detailed conflicts for an employee and event
     *
     * @param IndufastEmployee $employee
     * @param IndufastCalendarEvent $event
     * @param string $availability
     * @param IndufastCalendarEvent[] $allIndufastEvents
     * @return array Array of conflict details
     */
    private static function detectConflicts(IndufastEmployee $employee, IndufastCalendarEvent $event, string $availability, array $allIndufastEvents): array {
      $conflicts = [];
      $eventDate = date('Y-m-d', strtotime($event->start));
      $eventStart = new \DateTime($event->start);
      $eventEnd = new \DateTime($event->end);

      // 1. Check availability conflict (existing logic)
      if ($employee->hasConflict($event, $availability)) {
        $conflicts[] = [
          'type' => 'availability_conflict',
          'description' => "Employee availability ({$availability}) conflicts with event day part ({$event->day_part})",
          'availability' => $availability,
          'event_day_part' => $event->day_part,
          'severity' => 'high'
        ];
      }

      // 2. Check for time overlaps with other Indufast events
      foreach ($allIndufastEvents as $otherEvent) {
        if ($otherEvent->id === $event->id) continue;

        // Check if this employee is assigned to the other event
        $otherEventEmployees = $otherEvent->employees();
        $isEmployeeInOtherEvent = array_any($otherEventEmployees, fn($ee) => $ee->employee_id === $employee->id);

        if (!$isEmployeeInOtherEvent) continue;

        $otherEventStart = new \DateTime($otherEvent->start);
        $otherEventEnd = new \DateTime($otherEvent->end);

        // Check for time overlap
        if ($eventStart < $otherEventEnd && $eventEnd > $otherEventStart) {
          $conflicts[] = [
            'type' => 'time_overlap',
            'description' => "Time overlap with another event",
            'conflicting_event_id' => $otherEvent->id,
            'conflicting_event_start' => $otherEvent->start,
            'conflicting_event_end' => $otherEvent->end,
            'conflicting_project_id' => $otherEvent->project_id,
            'overlap_start' => max($eventStart, $otherEventStart)->format('Y-m-d H:i:s'),
            'overlap_end' => min($eventEnd, $otherEventEnd)->format('Y-m-d H:i:s'),
            'severity' => 'critical'
          ];
        }
      }

      // 3. Check for same project, same day conflict (for work events only)
      if ($event->type === 'work') {
        foreach ($allIndufastEvents as $otherEvent) {
          if ($otherEvent->id === $event->id) continue;
          if ($otherEvent->type !== 'work') continue;
          if ($otherEvent->project_id !== $event->project_id) continue;

          $otherEventDate = date('Y-m-d', strtotime($otherEvent->start));
          if ($eventDate === $otherEventDate) {
            // Check if this employee is in both events
            $otherEventEmployees = $otherEvent->employees();
            $isEmployeeInOtherEvent = array_any($otherEventEmployees, fn($ee) => $ee->employee_id === $employee->id);

            if ($isEmployeeInOtherEvent) {
              $conflicts[] = [
                'type' => 'same_project_same_day',
                'description' => "Employee assigned to multiple work events of the same project on the same day",
                'conflicting_event_id' => $otherEvent->id,
                'conflicting_event_start' => $otherEvent->start,
                'conflicting_event_end' => $otherEvent->end,
                'project_id' => $event->project_id,
                'date' => $eventDate,
                'severity' => 'high'
              ];
            }
          }
        }
      }

      return $conflicts;
    }

    public static function cleanupPastEvents(): void {
      $event = IndufastCalendarEvent::getTablename();
      $eventEmployee = IndufastCalendarEventEmployee::getTablename();

      // update all events from before yesterday
      $yesterday = date('Y-m-d 0:00:00', strtotime('-1 day'));
      $query = <<<SQL
        UPDATE $eventEmployee 
        SET has_conflicts = 0 
        WHERE calendar_event_id IN (
          SELECT id FROM $event 
          WHERE start < '$yesterday'
        )
      SQL;
      DBConn::db_link()->query($query);
    }
  }