<script setup>
import {computed, onMounted, ref, watch} from "vue";
import {useRouter, useRoute} from "vue-router";
import {useTitle} from "vue-page-title";
import createApiService from "@/services/api.js";
import {definePage} from "unplugin-vue-router/runtime";
import { vMaska } from "maska/vue"

const router = useRouter();
const api = createApiService(router);
const {setTitle} = useTitle();
const route = useRoute();
const summary = ref();
const workdays = ref();
const changedWorkdays = ref({});
const loading = ref({});
const user = ref();
const openDays = ref([]);
const newLine = ref({
  begin: '',
  end: '',
});

definePage({
  name: "accredis-details",
  meta: {
    title: 'Accredis',
    requiresAuth: true,
  },
})

onMounted(() => {
  refreshWorkdays();
});

const refreshWorkdays = () => {
  changedWorkdays.value = {}

  api
    .get("getWorkdays?" + new URLSearchParams({
      user_id: route.params.user,
      year: route.params.year,
      month: route.params.month,
    }).toString())
    .then((response) => {
      workdays.value = response.data.data;
      openDays.value = [];
      for (let i in workdays.value) {
        if (workdays.value[i].status === 'new') {
          openDays.value.push(parseInt(i));
        }
      }
      refreshWorkdaySummary();
    })
    .catch((error) => {
      console.log(error);
    });

  api
    .get("getUser?user_id=" + route.params.user)
    .then((response) => {
      user.value = response.data.data;
      setTitle(user.value.name);
    })
    .catch((error) => {
      console.log(error);
    });
}

const refreshWorkdaySummary = () => {
  api.get("getWorkdaySummary?" + new URLSearchParams({
    user_id: route.params.user,
    year: route.params.year,
    month: route.params.month,
  }).toString())
    .then((response) => {
      summary.value = response.data.data;
    })
    .catch((error) => {
      console.log(error);
    });
}

const updateCalculation = async (workday) => {
  let workdayLinesData = {};
  workday.lines.forEach((line) => {
    workdayLinesData[line.id] = {
      void: line.void,
    };
  });

  loading.value[workday.id] = true;

  let json = JSON.stringify({
    selectedTravelToEndLineId: workday.travel_to_end_line_id,
    selectedTravelFromStartLineId: workday.travel_from_start_line_id,
    workdayLinesData: workdayLinesData,
  });
  await api
    .post("calculateWorkday?workday_id=" + workday.id, json)
    .then((response) => {
      const workdayIndex = workdays.value.findIndex((workdayLocal) => workdayLocal.id === workday.id);
      workdays.value[workdayIndex] = response.data.data;
    })
    .catch((error) => {
      console.log(error);
    });

  loading.value[workday.id] = false;
}

const saveWorkday = (workday) => {
  loading.value[workday.id] = true;
  let workdayLinesData = {};
  workday.lines.forEach((line) => {
    workdayLinesData[line.id] = {
      void: line.void,
    };
  });

  let json = JSON.stringify({
    selectedTravelToEndLineId: workday.travel_to_end_line_id,
    selectedTravelFromStartLineId: workday.travel_from_start_line_id,
    workdayLinesData: workdayLinesData,
    remark: workday.remark,
    status: workday.status,
  });
  api
    .post("updateWorkday?workday_id=" + workday.id, json)
    .then((response) => {
      const workdayIndex = workdays.value.findIndex((workdayLocal) => workdayLocal.id === workday.id);
      workdays.value[workdayIndex] = response.data.data;
      delete changedWorkdays.value[workday.id];
      if (workdays.value[workdayIndex].status === 'processed') {
        openDays.value = openDays.value.filter((day) => day !== workdayIndex);
      }
      refreshWorkdaySummary()
      loading.value[workday.id] = false;
    })
    .catch((error) => {
      loading.value[workday.id] = false;
      console.log(error);
    });
}

const updateWorkday = (workday, newWorkday) => {
  const workdayIndex = workdays.value.findIndex((workdayLocal) => workdayLocal.id === workday.id);
  workdays.value[workdayIndex] = newWorkday;
  delete changedWorkdays.value[workday.id];
  if (workdays.value[workdayIndex].status === 'processed') {
    openDays.value = openDays.value.filter((day) => day !== workdayIndex);
  }
  refreshWorkdaySummary()
}
const saveWorkdayLine = (workday) => {
  loading.value[workday.id] = true;
  let json = JSON.stringify({
    start: newLine.value.start,
    end: newLine.value.end,
  });
  api
    .post("createWorkdayLine?workday_id=" + workday.id, json)
    .then((response) => {
      console.log(response);
      updateWorkday(workday, response.data.data);
      loading.value[workday.id] = false;
    })
    .catch((error) => {
      loading.value[workday.id] = false;
      console.log(error);
    });
}

const saveAllWorkdays = () => {
  for (const workdayId in changedWorkdays.value) {
    saveWorkday(workdays.value.find((workday) => workday.id === parseInt(workdayId)));
  }
};

const setTravelTo = (item) => {
  const workday = workdays.value.find((workday) => workday.id === item.workday_id);
  if (workday.travel_to_end_line_id !== item.id) {
    workday.travel_to_end_line_id = item.id;
    changedWorkdays.value[workday.id] = true;
    updateCalculation(workday);
  }
};

const setTravelFrom = (item) => {
  const workday = workdays.value.find((workday) => workday.id === item.workday_id);
  if (workday.travel_from_start_line_id !== item.id) {
    workday.travel_from_start_line_id = item.id;
    changedWorkdays.value[workday.id] = true;
    updateCalculation(workday);
  }
};

const voidLine = (item) => {
  item.void = !item.void;

  const workday = workdays.value.find((workday) => workday.id === item.workday_id);

  changedWorkdays.value[workday.id] = true;
  updateCalculation(workday);
};

const getIcon = (workday, color = false) => {
  if (changedWorkdays.value[workday.id]) {
    return (color) ? 'indufastRed' : 'mdi-content-save';
  }

  if (workday.status === 'processed') {
    return (color) ? 'indufastGreen' : 'mdi-check';
  }

  return (color) ? 'black' : 'mdi-alert-box-outline';
}

const rowProps = (row) => {
  return {
    class: {
      "workday-line": true,
      "void": row.item.void,
      "travel-from": !row.item.void && row.item.type === 'travel-from',
      "travel-to": !row.item.void && row.item.type === 'travel-to',
    },
  };
};

const dayHeaders = [
  {title: "Start", value: "start", cellProps: {class: "no-wrap"}},
  {title: "Eind", value: "end", cellProps: {class: "no-wrap"}},
  {title: "Duur", value: "duration", cellProps: {class: "no-wrap"}},
  {title: "Voertuig", value: "vehicle", cellProps: {class: "no-wrap"}},
  {title: "Van", value: "start_address"},
  {title: "Naar", value: "end_address"},
  {title: "Afstand", value: "distance", cellProps: {class: "no-wrap"}},
  {title: "Acties", value: "actions", cellProps: {class: "no-wrap"}},
];

const workdaySummary = computed(() => {
  return [
    {title: 'Werknemer', value: user.value?.name},
    {title: 'Aantal gewerkte dagen', value: summary.value?.count},
    {title: 'Totaal gewerkte uren', value: summary.value?.total},
    {title: 'Uren buiten 6:00 en 18:00', value: summary.value?.totalOutsideWorkingHours},
    {title: 'Reistijd', value: summary.value?.totalTravel},
  ];
});

watch(route, () => {
  refreshWorkdays();
})

</script>

<template>
  <v-container fluid>
    <v-row v-for="(item, index) in workdaySummary" :key="index" dense>
      <v-col cols="2">
        {{ item.title }}
      </v-col>
      <v-col>
        {{ item.value }}
      </v-col>
    </v-row>
  </v-container>

  <v-expansion-panels
    v-model="openDays"
    multiple
  >
    <v-expansion-panel v-for="(day, index) in workdays" :key="day.id">
      <v-expansion-panel-title>
        <v-icon
          class="mr-1"
          :color="getIcon(day, true)"
        >
          {{ getIcon(day) }}
        </v-icon>
        {{ $filters.ucFirst($filters.formatDate(day.date)) }}<v-spacer /><span v-if="day.status !== 'new' && openDays.indexOf(index) === -1">Uren gewerkt: {{ day.workdayDurationNet }} - Reistijd: {{ day.travelDurationNet }}</span>
      </v-expansion-panel-title>
      <v-expansion-panel-text class="pa-6">
        <v-row>
          <v-col cols="2" class="summary">
            <h4>
              Werkdag
            </h4>
            <v-table v-if="day.workdayStart">
              <tr>
                <th>Werktijd</th>
                <td>{{ day.workdayStart }} - {{ day.workdayEnd }}</td>
              </tr>
              <tr>
                <td>Bruto</td>
                <td>{{ day.workdayDuration }}</td>
              </tr>
              <tr>
                <th>Pauze</th>
                <td>{{ day.workdayPause }} minuten</td>
              </tr>
              <tr class="netDuration">
                <th>Netto</th>
                <td>{{ day.workdayDurationNet }}</td>
              </tr>
              <tr class="netDuration" v-if="day.workdayOutsideWorkingHours !== '00:00:00'">
                <th>Buiten 6-18</th>
                <td>{{ day.workdayOutsideWorkingHours }}</td>
              </tr>
            </v-table>
            <h4>Reistijd</h4>
            <v-table v-if="day.travelToStart || day.travelFromStart">
              <tr>
                <th>Heenreis</th>
                <td>{{ day.travelToStart }} - {{ day.travelToEnd }}</td>
              </tr>
              <tr>
                <th>Bruto</th>
                <td>{{ day.travelToDuration }}</td>
              </tr>
              <tr class="netDuration">
                <th>Netto</th>
                <td>{{ day.travelToDurationNet }}</td>
              </tr>
              <tr>
                <th>Terugreis</th>
                <td>{{ day.travelFromStart }} - {{ day.travelFromEnd }}</td>
              </tr>
              <tr>
                <th>Bruto</th>
                <td>{{ day.travelFromDuration }}</td>
              </tr>
              <tr class="netDuration">
                <th>Netto</th>
                <td>{{ day.travelFromDurationNet }}</td>
              </tr>
              <tr class="totalDuration">
                <th>Totaal</th>
                <td>{{ day.travelDurationNet }}</td>
              </tr>
            </v-table>
          </v-col>
          <v-col>
            <v-data-table
              :items="day.lines"
              :headers="dayHeaders"
              item-key="id"
              hide-default-footer
              :row-props="rowProps"
              density="compact"
              :loading="loading[day.id]"
              items-per-page="-1"
            >
              <template #item.distance="{ value }">
                {{ $filters.formatDistance(value) }}
              </template>

              <template #item.actions="{ item }">
                <v-icon @click="setTravelTo(item)" :disabled="!item.canBeSetAsTravelToEnd" color="indufastGreen">
                  mdi-arrow-collapse-down
                </v-icon>
                <v-icon @click="setTravelFrom(item)" :disabled="!item.canBeSetAsTravelFromStart" color="indufastCyan">
                  mdi-arrow-collapse-up
                </v-icon>
                <v-icon @click="voidLine(item)" :disabled="!item.canBeVoid" color="indufastRed">
                  {{ item.void ? 'mdi-undo' : 'mdi-close' }}
                </v-icon>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-textarea
              v-model="day.remark"
              label="Opmerkingen"
              variant="outlined"
              rows="1"
              auto-grow
              hide-details
              @keydown="changedWorkdays[day.id] = true"
            />
          </v-col>
        </v-row>
        <v-row class="mt-0">
          <v-col>
            <v-card-actions>
              <v-spacer />
              <v-dialog max-width="500">
                <template v-slot:activator="{ props: activatorProps }">
                  <v-btn
                    v-bind="activatorProps"
                    prepend-icon="mdi-plus"
                    :disabled="day.status !== 'new'"
                  >
                    Regel toevoegen
                  </v-btn>
                </template>
                <template v-slot:default="{ isActive }">
                  <v-card>
                    <v-card-title class="bg-primary">Regel toevoegen op {{day.date}}</v-card-title>
                    <v-card-text>
                      <v-text-field
                        v-model="newLine.start"
                        v-maska="'##:##'"
                        placeholder="08:00"
                        label="Van"
                        prepend-icon="mdi-clock-time-four-outline"
                      />
                      <v-text-field
                        v-model="newLine.end"
                        v-maska="'##:##'"
                        placeholder="17:00"
                        label="Tot"
                        prepend-icon="mdi-clock-time-four-outline"
                      />
                    </v-card-text>
                    <v-card-actions>
                      <v-btn
                        text="Annuleren"
                        @click="isActive.value = false"
                      />
                      <v-btn
                        text="Opslaan"
                        color="primary"
                        variant="elevated"
                        @click="saveWorkdayLine(day)"
                      />
                    </v-card-actions>
                  </v-card>
                </template>
              </v-dialog>
              <v-checkbox
                v-model="day.status"
                label="Afgerond"
                density="compact"
                false-value="new"
                true-value="processed"
                class="mr-2"
                color="primary"
                hide-details
                @change="changedWorkdays[day.id] = true"
              />
              <v-btn
                color="primary"
                variant="elevated"
                prepend-icon="mdi-content-save"
                :disabled="!changedWorkdays[day.id]"
                :loading="loading[day.id]"
                @click="saveWorkday(day)"
              >
                Opslaan
              </v-btn>
            </v-card-actions>
          </v-col>
        </v-row>
      </v-expansion-panel-text>
    </v-expansion-panel>
  </v-expansion-panels>
  <v-fab
    v-if="Object.keys(changedWorkdays).length"
    size="large"
    color="primary"
    location="top right"
    prepend-icon="mdi-content-save"
    app
    @click="saveAllWorkdays"
  >
    Sla alles op ({{ Object.keys(changedWorkdays).length }})
  </v-fab>
</template>

<style lang="scss">
tr.workday-line td:first-child {
  border-left: 10px solid lightgray;
}

tr.void td:first-child {
  border-left: 10px solid rgba(var(--v-theme-indufastRed), .75);
}

tr.void {
  background-color: rgba(var(--v-theme-indufastRed), .1);
}

tr.travel-to td:first-child {
  border-left: 10px solid rgba(var(--v-theme-indufastGreen), .75);
}

tr.travel-to {
  background-color: rgba(var(--v-theme-indufastGreen), .1);
}

tr.travel-from td:first-child {
  border-left: 10px solid rgba(var(--v-theme-indufastCyan), .75);
}

tr.travel-from {
  background-color: rgba(var(--v-theme-indufastCyan), .1);
}

.v-icon--disabled {
  color: grey !important;
}

.workday-title {
  background-color: rgba(var(--v-theme-indufastCyan), .1);
}

td.no-wrap {
  white-space: nowrap;
  width: 0;
}
tr.netDuration,
tr.totalDuration {
  font-weight: bold;
}

.summary th {
  font-weight: normal;
  text-align: left;
}
.summary .totalDuration th {
  font-weight: bold;
}
.summary tr.totalDuration th,
.summary tr.totalDuration td,
.summary tr.netDuration th,
.summary tr.netDuration td {
  padding-bottom: 10px !important;
}
</style>
