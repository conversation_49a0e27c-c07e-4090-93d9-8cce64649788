/**
 * plugins/index.js
 *
 * Automatically included in `./src/main.js`
 */

// Plugins
import vuetify from './vuetify'
import pinia from '@/stores'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import router from '@/router'
import {pageTitle} from 'vue-page-title';
import VueDatePicker from "@vuepic/vue-datepicker";
import '@vuepic/vue-datepicker/dist/main.css';

pinia.use(piniaPluginPersistedstate)

export function registerPlugins(app) {
  app
    .component('VueDatePicker', VueDatePicker)
    .use(vuetify)
    .use(router)
    .use(pinia)
    .use(
      pageTitle({
        suffix: '- Indufast portal',
        mixin: true,
        router
      })
    );

}
