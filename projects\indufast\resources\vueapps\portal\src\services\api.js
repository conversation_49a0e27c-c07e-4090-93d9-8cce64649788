import axios from 'axios';

const createApiService = (router) => {
  const api = axios.create({
    baseURL: '/nl/api', // Adjust the base URL as needed
    timeout: 10000,
  });

  api.interceptors.response.use(
    response => response,
    error => {
      if (error.response) {
        if (error.response.status === 401) {
          router.push({ name: '/login' });
        } else {
          console.error(`API error: ${error.response.status} - ${error.response.data.message}`);
        }
      } else {
        console.error('API error: ', error.message);
      }
      return Promise.reject(error);
    }
  );

  return api;
};

export default createApiService;
