<script setup>
import {onMounted, ref, watch} from "vue";
import {useRouter} from 'vue-router';
import createApiService from '@/services/api';
import {definePage} from "unplugin-vue-router/runtime";
import {employeeTypes, industryTypes} from "@/helpers/constants.js";
import {useTitle} from "vue-page-title";
import {translateErrors} from "@/helpers/translateErrors.js";
import {useSnackbarStore} from '@/stores/snackbar';
import {sortNullLast} from "@/helpers/helpers.js";
import SearchBox from "@/components/SearchBox.vue";

const router = useRouter();
const api = createApiService(router);

let employees = ref([]);
let loading = ref(false);
let editEmployeeDialog = ref(false);
let editEmployeeDialogBusy = ref(false);
let editEmployeeData = ref({});
let errors = ref({});
let search = ref('');
const {title} = useTitle();

const snackbarStore = useSnackbarStore();
definePage({
  meta: {
    title: 'Medewerkers',
  },
})

onMounted(() => {
  updateEmployees()
});

const sortType = (a, b) => {
  return employeeTypes.find(type => type.value === a)?.title.localeCompare(employeeTypes.find(type => type.value === b)?.title);
}

const employeeHeader = [
  {title: "Naam", value: "name", sortable: true, nowrap: true, maxWidth: 350},
  {title: "Type", value: "type", cellProps: {class: "no-wrap"}, sort: sortType},
  {title: "E-mail", value: "email", sortable: true},
  {title: "Extra e-mail", value: "extra_email", sort: sortNullLast},
  {title: "Rang", value: "rank", cellProps: {class: "no-wrap"}, sortable: true, sortRaw(a, b) {
    return (a.rank !== b.rank) ? a.rank.localeCompare(b.rank) : a.rank_number - b.rank_number;
  }},
  {title: "Branches", value: "industries", cellProps: {class: "no-wrap"}},
  {title: "Actief", value: "active", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Contactpersoon bouwplaats", value: "team_lead", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Privé", value: "private", cellProps: {class: "no-wrap center"}, sortable: true},
  {title: "Acties", value: "actions", cellProps: {class: "no-wrap center"}},
];

const editEmployee = (employee) => {
  editEmployeeDialog.value = true;
  editEmployeeData.value = {...employee};
}

const newEmployee = () => {
  editEmployeeDialog.value = true;
  editEmployeeData.value = {
    active: true,
    team_lead: false,
    private: false,
    rank_number: 1,
  }
}

const submitEditEmployee = () => {
  editEmployeeDialogBusy.value = true;

  let uri = (editEmployeeData.value.id) ? "employeeUpdate?id=" + editEmployeeData.value.id : "employeeCreate";
  api
    .post(uri, JSON.stringify(editEmployeeData.value))
    .then((response) => {
      editEmployeeData.value.id = response.data.data.id;
      editEmployeeDialog.value = false;
      editEmployeeDialogBusy.value = false;
      snackbarStore.showMessage("Medewerker succesvol opgeslagen!", "success");
      updateEmployees();
    })
    .catch((error) => {
      editEmployeeDialogBusy.value = false;
      errors.value = error.response.data.data
      snackbarStore.showMessage("Er is een fout opgetreden bij het opslaan!", "error");
      console.log(error);
    });
}

const updateEmployees = () => {
  loading.value = true;
  api
    .get("employeeList")
    .then((response) => {
      loading.value = false;
      employees.value = response.data.data;
    })
    .catch((error) => {
      loading.value = false;
      errors.value = error.response.data.data
      console.log(error);
    });
}

watch(editEmployeeDialog, (val) => {
  if (!val) {
    errors.value = {};
  }
})

const removeIndustry = (item) => {
  editEmployeeData.industries = editEmployeeData.industries.filter(find => find !== item);
}

</script>

<template>
  <v-card>
    <v-card-title class="space-between pt-4 pb-4">
      <span>
        {{ title }}
        <v-icon
          icon="mdi-refresh"
          size="small"
          color="primary"
          @click="updateEmployees"
        />
      </span>
      <div class="d-flex align-center ga-4">
        <search-box v-model="search" />
        <v-btn
          color="primary"
          prepend-icon="mdi-plus"
          @click="newEmployee"
        >
          Nieuwe medewerker
        </v-btn>
      </div>
    </v-card-title>
    <v-divider />
    <v-card-text>
      <v-data-table
        :headers="employeeHeader"
        :items="employees"
        item-key="id"
        density="compact"
        :loading="loading"
        must-sort
        :sort-by="[{key: 'name', order: 'asc'}]"
        :search="search"
        class="stickyFirstColumn stickyLastColumn rowHover"
      >
        <template #item.type="{ item }">
          {{ employeeTypes.find(type => type.value === item.type).title }}
        </template>
        <template #item.rank="{ item }">
          {{ item.rank }}{{ item.rank_number }}
        </template>
        <template #item.industries="{ item }">
          <v-icon
            v-for="(industry, index) in item.industries"
            :key="index"
            class="mr-1"
            :color="industryTypes.find(type => type.value === industry)?.color"
            :title="industryTypes.find(type => type.value === industry)?.title"
          >
            {{ industryTypes.find(type => type.value === industry)?.icon }}
          </v-icon>
        </template>
        <template #item.private="{ item }">
          <v-icon :color="item.private ? 'indufastGreen' : 'indufastRed'">
            {{ item.private ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.team_lead="{ item }">
          <v-icon :color="item.team_lead ? 'indufastGreen' : 'indufastRed'">
            {{ item.team_lead ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.active="{ item }">
          <v-icon :color="item.active ? 'indufastGreen' : 'indufastRed'">
            {{ item.active ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.actions="{ item }">
          <v-icon @click="editEmployee(item)" color="primary">
            mdi-pencil
          </v-icon>
        </template>
      </v-data-table>
      <v-dialog v-model="editEmployeeDialog" width="1000px">
        <v-card>
          <v-card-title class="pa-0">
            <v-toolbar color="primary" flat>
              <v-toolbar-title>
                {{ editEmployeeData.id ? 'Medewerker ' + editEmployeeData.name + ' bewerken' : 'Nieuwe medewerker' }}
              </v-toolbar-title>
              <v-toolbar-items>
                <v-btn
                  icon
                  @click="editEmployeeDialog = false"
                >
                  <v-icon icon="mdi-close" />
                </v-btn>
              </v-toolbar-items>
            </v-toolbar>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col>
                <v-card flat>
                  <v-card-title>Medewerker</v-card-title>
                  <v-card-text>
                    <v-text-field
                      v-model="editEmployeeData.name"
                      label="Naam"
                      :error-messages="translateErrors(errors.name, 'Naam')"
                      class="required mb-5"
                    />
                    <v-select
                      v-model="editEmployeeData.type"
                      label="Type"
                      :items="employeeTypes"
                      item-title="title"
                      item-value="value"
                      :error-messages="translateErrors(errors.type, 'Type')"
                      class="required mb-5"
                    />
                    <v-text-field
                      v-model="editEmployeeData.email"
                      label="E-mail"
                      :error-messages="translateErrors(errors.email, 'E-mail')"
                      class="required mb-5"
                    />
                    <v-text-field
                      v-model="editEmployeeData.extra_email"
                      label="Extra e-mail"
                      :error-messages="translateErrors(errors.extra_email, 'Extra e-mail')"
                    />
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col>
                <v-card flat>
                  <v-card-title class="pl-0">Rang</v-card-title>
                  <v-select
                    v-model="editEmployeeData.rank"
                    label="Rang"
                    :items="['A', 'B', 'C', 'D', 'E']"
                    :error-messages="translateErrors(errors.rank, 'Rang')"
                    class="required mb-5"
                  />
                  <v-slider
                    v-model="editEmployeeData.rank_number"
                    type="number"
                    min="1"
                    max="10"
                    step="1"
                    thumb-label="always"
                    show-ticks="always"
                    tick-size="2"
                    :error-messages="translateErrors(errors.rank_number, 'Rangnummer')"
                  />
                </v-card>
              </v-col>
              <v-col>
                <v-card flat>
                  <v-card-title>Overig</v-card-title>
                  <v-card-text>
                    <v-select
                      v-model="editEmployeeData.industries"
                      label="Branches"
                      :items="industryTypes"
                      item-title="title"
                      item-value="value"
                      multiple
                    >
                      <template #selection="{ item }">
                        <v-chip
                          :color="item.raw.color"
                          variant="flat"
                          :prepend-icon="item.raw.icon"
                        >
                          {{ item.title }}
                        </v-chip>
                      </template>
                    </v-select>
                    <v-switch
                      v-model="editEmployeeData.active"
                      color="indufastGreen"
                      base-color="indufastRed"
                      label="Actief"
                      hide-details
                    />
                    <v-switch
                      v-model="editEmployeeData.team_lead"
                      color="indufastGreen"
                      base-color="indufastRed"
                      label="Contactpersoon bouwplaats"
                      hide-details
                    />
                    <v-switch
                      v-model="editEmployeeData.private"
                      color="indufastGreen"
                      base-color="indufastRed"
                      label="Privé"
                      hide-details
                    />
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-btn
              variant="elevated"
              color="primary"
              prepend-icon="mdi-content-save"
              :loading="editEmployeeDialogBusy"
              @click="submitEditEmployee"
            >
              Opslaan
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-card-text>
  </v-card>
</template>
