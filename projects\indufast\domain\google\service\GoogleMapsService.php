<?php

namespace domain\google\service;

use Google\Client;

class GoogleMapsService {
    protected string $apiKey;

    public function __construct() {
      $this->apiKey = \Config::get('GOOGLE_API_MAPS_KEY');
    }

    public function getLocations($searchQuery) {
      $url = 'https://maps.googleapis.com/maps/api/place/autocomplete/json';
      $params = [
          'input' => $searchQuery,
          'types' => 'address',
          'language' => 'nl',
          'key' => $this->apiKey,
          'components' => 'country:nl|country:be|country:de|country:lu',
      ];

      $queryString = http_build_query($params);
      $fullUrl = $url . '?' . $queryString;

      $ch = curl_init();
      curl_setopt($ch, CURLOPT_URL, $fullUrl);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

      $response = curl_exec($ch);
      curl_close($ch);

      return json_decode($response, true);
    }
}