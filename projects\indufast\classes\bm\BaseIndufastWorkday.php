<?php
class BaseIndufastWorkday extends AppModel {

  const DB_NAME = '';
  const TABLE_NAME = 'indufast_workday';
  const OM_CLASS_NAME = 'IndufastWorkday';
  const columns = ['id', 'user_id', 'date', 'travel_to_end_line_id', 'travel_from_start_line_id', 'type', 'status', 'remark', 'insertTS', 'updateTS'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'user_id'                     => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'date'                        => ['type' => 'date', 'length' => '', 'null' => false],
    'travel_to_end_line_id'       => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'travel_from_start_line_id'   => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'type'                        => ['type' => 'enum', 'length' => '4', 'null' => true, 'enums' => ['travel','leave','special-leave','sick']],
    'status'                      => ['type' => 'enum', 'length' => '3', 'null' => false, 'enums' => ['new','processed','synced']],
    'remark'                      => ['type' => 'text', 'length' => '', 'null' => true],
    'insertTS'                    => ['type' => 'datetime', 'length' => '', 'null' => false],
    'updateTS'                    => ['type' => 'datetime', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $user_id, $date, $travel_to_end_line_id, $travel_from_start_line_id, $type, $status, $remark, $insertTS, $updateTS;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->status = 'new';
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkday[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkday[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return IndufastWorkday[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkday
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return IndufastWorkday
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}