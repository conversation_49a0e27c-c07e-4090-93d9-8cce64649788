<?php

  use domain\accredis\service\AccredisDriverService;

  class accredisIndufastActions extends gsdActions {

    public array $errors;
    public array $warnings;

    public function executeAccredis(): void {
      $errors = [];
      $warnings = [];
      $messages = [];

      if (isset($_FILES['bestand_accredis_xml_export'])) {
        try {
          $filename = $this->handleAccredisXMLUpload();
          $trips = $this->parseAccredisXMLUpload($filename);
          $this->saveTrips($trips, $errors, $warnings, $messages);
        }
        catch (Exception $e) {
          $errors[] = $e->getMessage();
        }
      }

      MessageFlashCoordinator::addMessages($messages);
      $this->errors = array_filter($errors);
      $this->warnings = array_filter($warnings);
    }

    /**
     * @throws Exception
     */
    private function handleAccredisXMLUpload(): string {
      $file_uploader = new Uploader("accredis_xml_export", reconstructQuery(), DIR_TEMP);
      $file_uploader->setCreateUniqueFilenameBool(true);
      $file_uploader->setShowuploadbut(true);
      $file_uploader->setAllowed(['text/xml' => 'xml']);
      $file_uploader->setMaxfilesizeByMb(10);
      $result = $file_uploader->parseUpload('', false);

      if ($file_uploader->hasErrors()) {
        throw new Exception('Fout bij het uploaden: ' . $file_uploader->getErrorsFormatted());
      }
      if ($result) {
        $file_uploader->moveUploadedFile();
        return $file_uploader->getFilepath();
      }

      throw new Exception('Fout bij het uploaden.');
    }

    /**
     * Parse the XML and return an array grouped by driver and date.
     *
     * @throws Exception
     */
    private function parseAccredisXMLUpload(string $filename): array {
      $trips = [];

      if (!$xml = simplexml_load_file($filename)) {
        throw new Exception('Kan XML niet parsen.');
      }

      // Gather all trips.
      foreach ($xml->xpath('/TripList/Trip') as $trip) {
        $start = new DateTime((string)$trip->Start);
        $end = new DateTime((string)$trip->End);

        $workdayLine = new IndufastWorkdayLine();
        $workdayLine->external_id = (int)$trip->Number;
        $workdayLine->start = $start->format('H:i:s');
        $workdayLine->end = $end->format('H:i:s');
        $workdayLine->distance = (float)$trip->Distance;
        $workdayLine->vehicle = (string)$trip->Vehicle;
        $workdayLine->start_address = implode(', ', array_filter([
          (string)$trip->FromStreet,
          (string)$trip->FromPostcode,
          (string)$trip->FromCity,
          (string)$trip->FromCountry,
        ]));
        $workdayLine->end_address = implode(', ', array_filter([
          (string)$trip->ToStreet,
          (string)$trip->ToPostcode,
          (string)$trip->ToCity,
          (string)$trip->ToCountry,
        ]));

        $trips[(string)$trip->Driver][(string)$trip->Date][] = $workdayLine;
      }

      return $trips;
    }

    /**
     * Store the workdays and related workday lines into the database.
     *
     * @throws GsdDbException
     * @throws GsdException
     */
    private function saveTrips(array $trips, array &$errors, array &$warnings, array &$messages): void {
      $drivers = AccredisDriverService::getAll();
      $workdayInsert = 0;
      $workdayLineInsert = 0;

      foreach ($trips as $driver => $section) {
        if (!isset($drivers[$driver])) {
          $warnings[] = sprintf('Onbekende chauffeur: %s', $driver);
          continue;
        }

        /** @var IndufastWorkdayLine[] $workdayLines */
        foreach ($section as $date => $workdayLines) {

          // Load or create the workday.
          if (!$workday = IndufastWorkday::find_by(['date' => $date, 'user_id' => $drivers[$driver]['user']->id])) {
            $workday = new IndufastWorkday();
            $workday->date = $date;
            $workday->user_id = $drivers[$driver]['user']->id;
            if ($workday->save($errors)) {
              $workdayInsert++;
            }
          }

          foreach ($workdayLines as $workdayLine) {
            $workdayLine->workday_id = $workday->id;

            // Create a new workday line?
            if (!IndufastWorkdayLine::find_by(['workday_id' => $workday->id, 'external_id' => $workdayLine->external_id])) {
              if ($workdayLine->save($errors)) {
                $workdayLineInsert++;
              }
            }
          }
        }
      }

      $messages[] = sprintf('%d werkdagen aangemaakt en %d ritten toegevoegd.', $workdayInsert, $workdayLineInsert);
    }

  }