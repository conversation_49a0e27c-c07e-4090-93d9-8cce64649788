<?php

  class userIndufastActions extends userActions {

    /**
     * @return UserProfile[]
     */
    protected function getUserProfiles(User $user, Organisation $organisation): array {
      return UserProfile::getUserProfileByUserid($user->id);
    }

    /**
     * Update the user profile data.
     *
     * @throws GsdDbException
     * @throws GsdException
     */
    public function saveUserProfiles(array $user_profiles, User $user, Organisation $organisation): void {
      foreach (Config::get('USER_PROFILE_CODES', true) ?? [] as $key => $label) {
        if (!$userProfile = $user_profiles[$key] ?? null) {
          $userProfile = new UserProfile();
          $userProfile->user_id = $user->id;
          $userProfile->code = $key;
          $userProfile->type = UserProfileModel::TYPE_OTHER;
        }

        $userProfile->value = $_POST[$key] ?? null;

        if (!empty($userProfile->value)) {
          $userProfile->save();
          $user_profiles[$key] = $userProfile;
        } elseif ($userProfile->id) {
          $userProfile->destroy();
        }
      }
    }

  }