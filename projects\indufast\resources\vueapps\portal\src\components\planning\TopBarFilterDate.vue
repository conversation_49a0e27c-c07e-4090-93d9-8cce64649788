<script setup>
import {onBeforeMount, onUnmounted, ref, watch} from 'vue';
import {addDays, addMonths, format} from 'date-fns';
import {useRoute, useRouter} from 'vue-router';
import {useMagicKeys} from '@vueuse/core';
import {debounceMseconds} from '@/helpers/constants';

const router = useRouter();
const route = useRoute();
const keys = useMagicKeys();
const keyPrev = keys['Ctrl+Left'];
const keyNext = keys['Ctrl+Right'];
const date = ref();
const menuOpen = ref(false);
const debounce = ref();
const calendarView = ref(!route.hash || route.hash === '#week' ? 'week' : 'month');
const isCalendar = ref(route.name === '/calendar');

onBeforeMount(() => {
  date.value = route.params.date?.length ? route.params.date : format(new Date(), 'yyyy-MM-dd');
});

onUnmounted(() => {
  clearTimeout(debounce.value);
});

const selectPrevDate = () => {
  if (isCalendar.value) {
    if (calendarView.value === 'week') {
      date.value = format(addDays(date.value, -7), 'yyyy-MM-dd');
    }
    else {
      date.value = format(addMonths(date.value, -1), 'yyyy-MM-dd');
    }
  }
  else {
    date.value = format(addDays(date.value, -1), 'yyyy-MM-dd');
  }
};

const selectNextDate = () => {
  if (isCalendar.value) {
    if (calendarView.value === 'week') {
      date.value = format(addDays(date.value, 7), 'yyyy-MM-dd');
    }
    else {
      date.value = format(addMonths(date.value, 1), 'yyyy-MM-dd');
    }
  }
  else {
    date.value = format(addDays(date.value, 1), 'yyyy-MM-dd');
  }
};

const selectToday = () => {
  date.value = format(new Date(), 'yyyy-MM-dd');
};

const toggleCalendarView = () => {
  if (calendarView.value === 'week') {
    calendarView.value = 'month';
  } else {
    calendarView.value = 'week';
  }
};

const updateRoute = () => {
  menuOpen.value = false;

  clearTimeout(debounce.value);
  debounce.value = setTimeout(() => {
    router.replace({
      name: route.name,
      params: {
        date: date.value,
      },
      hash: '#' + calendarView.value
    });
  }, debounceMseconds);
};

// Watch for keyboard shortcuts to navigate between days.
watch(keyPrev, (pressed) => {
  if (pressed) {
    selectPrevDate();
  }
});

watch(keyNext, (pressed) => {
  if (pressed) {
    selectNextDate();
  }
});

watch(date, () => {
  updateRoute();
});

watch(calendarView, () => {
  updateRoute();
});

watch(route, (to) => {
  date.value = (!to.params.date) ? format(new Date(), 'yyyy-MM-dd') : to.params.date;
  isCalendar.value = route.name === '/calendar';
}, {flush: 'pre', immediate: true, deep: true});

</script>

<template>
  <v-btn
    icon="mdi-chevron-left"
    @click="selectPrevDate"
  />
  <v-menu
    v-model="menuOpen"
    location="bottom"
  >
    <template #activator="{ props }">
      <v-text-field
        v-model="date"
        v-bind="props"
        density="compact"
        hide-details
        class="date-select"
        readonly
        variant="outlined"
      >
        <template #append-inner>
          <v-btn
            v-bind="props"
            icon="mdi-calendar"
            size="small"
          />
        </template>
      </v-text-field>
    </template>

    <VueDatePicker
      v-model="date"
      model-type="yyyy-MM-dd"
      inline
      disable-time-picker
      auto-apply
      hide-offset-dates
      locale="nl-NL"
      week-num-name="#"
      week-numbers="iso"
      :enable-time-picker="false"
    />
  </v-menu>

  <v-btn
    icon="mdi-calendar-today"
    title="Ga naar vandaag"
    @click="selectToday"
  />
  <v-btn
    icon="mdi-chevron-right"
    @click="selectNextDate"
  />
  <v-btn
    v-if="isCalendar"
    :prepend-icon="calendarView !== 'week' ? 'mdi-calendar-week' : 'mdi-calendar-month'"
    :text="calendarView !== 'week' ? 'Week' : 'Maand'"
    @click="toggleCalendarView"
  />
</template>

<style lang="scss">
.vdp-datepicker__calendar {
  position: fixed;
}
</style>

<style>
.date-select {
  width: 160px;
}

.date-select .v-field--appended {
  padding-inline-end: 0;
}
</style>
