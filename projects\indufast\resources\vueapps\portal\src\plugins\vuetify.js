/**
 * plugins/vuetify.js
 *
 * Framework documentation: https://vuetifyjs.com`
 */

// Styles
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'

// Composables
import {createVuetify} from 'vuetify'

// https://vuetifyjs.com/en/introduction/why-vuetify/#feature-guides
export default createVuetify({
  defaults: {
    VDataTable: {
      itemsPerPage: 50,
      itemsPerPageOptions: [{value: 50, title: '50'}, {value: -1, title: 'Alle'}],
      itemsPerPageText: 'Items per pagina',
    },
  },
  theme: {
    themes: {
      light: {
        dark: false,
        colors: {
          primary: '#a8ba2a',
          'on-primary': '#ffffff',
          secondary: '#353535',
          success: '#a8ba2a',
          error: '#dd0015',

          indufastGreen: '#a8ba2a',
          indufastCyan: '#5bc0de',
          indufastRed: '#dd0015',
          indufastBlue: '#0060FF',
          indufastOrange: '#F4511E',
          indufastPurple: '#940096',
          indufastYellow: '#F7CE5E',

          GSD: '#a8ba2a',
          GSDBackground: '#f5f5f5',
          GSDMenuBackground: '#15151e',
          GSDMenuHighlight: '#272735',
        },
        variables: {
          'light-opacity': 0.5,
        },
      },
    },
  },
})
