<?php

  AppModel::loadModelClass('IndufastWorkdayLineModel');

  class IndufastWorkdayLine extends IndufastWorkdayLineModel {

    use PropertyCastTrait {
      castProperties as castPropertiesTrait;
    }

    public string $duration;
    public bool $canBeVoid;
    public bool $canBeSetAsTravelToEnd;
    public bool $canBeSetAsTravelFromStart;

    const CAST_PROPERTIES = [
      'id'          => 'int',
      'workday_id'  => 'int',
      'external_id' => 'int',
      'distance'    => 'float',
      'void'        => 'boolean',
      'from_db'     => 'hidden',
      'insertTS'    => 'hidden',
      'updateTS'    => 'hidden',
    ];

    public function setDefaults(): void {
      parent::setDefaults();
      $this->void = 0;
      $this->insertTS = date("Y-m-d H:i:s");
    }

    public function castProperties(IndufastWorkday $workday): void {
      $this->castPropertiesTrait();
      $this->setDuration();
      $this->setCanBeVoid($workday);

      $lineIds = array_map('intval', array_column($workday->lines, 'id'));
      $lineIndex = array_search($this->id, $lineIds);

      $this->canBeSetAsTravelToEnd = !$this->void && $workday->status == 'new' && $this->type != 'travel-from' && $lineIndex != count($workday->lines) - 1;
      $this->canBeSetAsTravelFromStart = !$this->void && $workday->status == 'new' && $this->type != 'travel-to' && $lineIndex != 0;
    }

    protected function setCanBeVoid(IndufastWorkday $workday): void {
      if ($workday->status !== 'new') {
        $this->canBeVoid = false;
        return;
      }
      if (empty($this->external_id) && $this->id != $workday->travel_to_end_line_id && $this->id != $workday->travel_from_start_line_id) {
        $this->canBeVoid = true;
        return;
      }

      $voidableTravelToId = null;
      foreach ($workday->lines as $line) {
        if (($this->type == 'travel-to' || $line->void) && $line->id != $workday->travel_to_end_line_id) {
          $voidableTravelToId = $line->id;
        }

        if ($line->id == $workday->travel_to_end_line_id || !$line->void) {
          break;
        }
      }

      $voidableTravelFromId = null;
      foreach (array_reverse($workday->lines) as $line) {
        if (($this->type == 'travel-from' || $line->void) && $line->id != $workday->travel_from_start_line_id) {
          $voidableTravelFromId = $line->id;
        }

        if ($line->id == $workday->travel_to_end_line_id || !$line->void) {
          break;
        }
      }

      $this->canBeVoid = ($this->id == $voidableTravelToId || $this->id == $voidableTravelFromId);
    }

    /**
     * @throws DateMalformedStringException
     */
    protected function setDuration(): void {
      $start = new DateTime($this->start);
      $end = new DateTime($this->end);
      $this->duration = $start->diff($end)->format('%H:%I:%S');
    }

  }