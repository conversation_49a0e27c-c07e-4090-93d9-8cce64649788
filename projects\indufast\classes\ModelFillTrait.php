<?php

  use Somnambulist\Components\Validation\ErrorBag;

  trait ModelFillTrait {

    /**
     * Fill the fillable model properties with the given data.
     */
    public function fill(array $data): self {
      $fillable = $this->getFillable();

      foreach ($data as $key => $value) {
        if (property_exists($this, $key) && in_array($key, array_keys($fillable))) {
          $rules = explode('|', $fillable[$key]);

          if (in_array('array', $rules) && is_array($value) && isset($this->fillableModels[$key])) {
            $this->{$key} = [];
            foreach ($value as $item) {

              // @TODO: trigger exception if not found?
              if (!isset($item->id) || !$object = ($this->fillableModels[$key])::find_by_id($item->id)) {
                $object = new $this->fillableModels[$key];
              }

              $object->fill((array)$item);
              $this->{$key}[] = $object;
            }
          }
          else {
            $this->{$key} = (is_string($value)) ? trim($value) : $value;
          }
        }
      }

      return $this;
    }

  }