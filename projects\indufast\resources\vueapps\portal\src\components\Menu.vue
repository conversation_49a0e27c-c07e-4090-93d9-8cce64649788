<script setup>
import {useAuthenticationStore} from "@/stores/authentication.js";

const authStore = useAuthenticationStore();

const items = [
  {title: 'Medewerkers', routeName: '/employees', icon: 'mdi-account-group-outline', hint: 'Alt + M'},
  {title: 'Uitloggen', routeName: '/logout', icon: 'mdi-logout'},
];

</script>

<template>
  <v-menu
    location="bottom"
  >
    <template #activator="{ props }">
      <span
        v-bind="props"
        class="username"
      >
        {{ authStore.getCurrentUserFullName() }}
      </span>
      <v-btn
        v-bind="props"
        icon
        color="primary"
      >
        <v-icon>mdi-triangle-small-down</v-icon>
      </v-btn>
    </template>

    <v-list class="menu-items">
      <v-list-item
        v-for="(item, index) in items"
        :key="index"
        :prepend-icon="item.icon"
        :to="{ name: item.routeName }"
        color="indufastRed"
      >
        <v-list-item-title :title="item.hint">
          {{ item.title }}
        </v-list-item-title>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<style>
.menu-items .v-list-item__prepend {
  width: 40px;
}

.username {
  user-select: none;
}
</style>
