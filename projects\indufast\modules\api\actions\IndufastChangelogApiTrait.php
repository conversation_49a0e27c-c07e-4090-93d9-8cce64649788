<?php

  use classes\ApiResponse;

  trait IndufastChangelogApiTrait {
    protected array $changeLog = [
      [
        'title' => 'Klikken in de kalender, zoeken, en planningsfixes.',
        'version' => '0.1.12',
        'date' => '2025-06-19',
        'changes' => [
          '#155: Toon event- of projectinfo vanuit de kalender.',
          '#161: Zoe<PERSON> in projecten en medewerkers.',
          '#162: Plannen meerdere dagen.',
          '#149: Google maps autocomplete voor adressen.',
        ],
      ],
      [
        'title' => 'Straaldagen, FTE per dag, kalender en algemene fixes.',
        'version' => '0.1.11',
        'date' => '2025-06-16',
        'changes' => [
          '#145: Straaldagen aan projecten toevoegen.',
          '#144: FTE per dag kunnen instellen.',
          '#102: Week- en maandoverzicht planning.',
          '#152: Google API: optimalisatie in aantal calls.',
          '#153: "Materiaal laden" clearable gemaakt.',
        ],
      ],
      [
        'title' => 'Unieke waardes, snellere beschikbaarheid en algemene fixes.',
        'version' => '0.1.10',
        'date' => '2025-06-11',
        'changes' => [
          '#140: Op unieke waardes controleren (projectnummer, e-mailadressen, etc).',
          '#112: Batch requests beschikbaarheid.',
          '#125: Code opschonen.',
          '#139: <EMAIL> heeft onvoldoende toegang.'
        ],
      ],
      [
        'title' => 'Bugfixes.',
        'version' => '0.1.9.3',
        'date' => '2025-06-07',
        'changes' => [
          '#135: Verwijderen van events zonder Google event geeft een error.',
          '#136: Booleans worden niet goed teruggegeven via de API.',
          '#137: Foutmelding bij het maken van een Google event zonder beschrijving.',
        ],
      ],
      [
        'title' => 'Bugfix.',
        'version' => '0.1.9.2',
        'date' => '2025-06-06',
        'changes' => [
          '#134: Fout bij opslaan nieuwe event.',
        ],
      ],
      [
        'title' => 'Nieuwe status + FTE bugfix.',
        'version' => '0.1.9.1',
        'date' => '2025-06-06',
        'changes' => [
          '#127: Nieuwe status: nog in te plannen.',
        ],
      ],
      [
        'title' => 'FTE en algemene verbeteringen.',
        'version' => '0.1.9',
        'date' => '2025-06-06',
        'changes' => [
          '#131: Tabellen: pagina\'s en sorteren.',
          '#126: Nieuw veld: FTE.',
          '#124: Laders en contactpersoon aan dag koppelen in plaats van project.',
          '#118: Samenvatting in agenda aanvullen.',
          '#105: Verwijderde of missende events in de Google Calendar opnieuw aanmaken.',
        ],
      ],
      [
        'title' => 'Verbeteringen in planning',
        'version' => '0.1.8',
        'date' => '2025-05-23',
        'changes' => [
          '#111: Bij droppen medewerker aan meerdere dagen van dat project kunnen koppelen.',
          '#91: Planning: info + events van de medewerker in een pop-up tonen.',
          '#120: Bij aanmaken datums niet naar planning kunnen navigeren.'
        ],
      ],
      [
        'title' => 'Bugfixes',
        'version' => '0.1.7',
        'date' => '2025-05-20',
        'changes' => [
          '#115: Probleem met browser cache na deploy.',
          '#116: Kleine verbeteringen aan afbeeldingen en fonts.',
        ],
      ],
      [
        'title' => 'Beschikbaarheid medewerkers actief bijhouden',
        'version' => '0.1.6',
        'date' => '2025-05-19',
        'changes' => [
          '#106: Achteraf gewijzigde beschikbaarheid tonen in planning.',
        ],
      ],
      [
        'title' => 'Bugfixes',
        'version' => '0.1.5',
        'date' => '2025-05-14',
        'changes' => [
          '#108: Gekoppelde medewerkers ontvangen geen uitnodiging.',
          '#109: In Google Calendar verwijderde events worden nooit hersteld.',
          '#107: Kan laatse persoon niet uit de planning verwijderen.',
          '#110: Herstel de 404-pagina.',
        ],
      ],
      [
        'title' => 'Feedback van de eerste release',
        'version' => '0.1.4',
        'date' => '2025-05-12',
        'changes' => [
          '#104: Projecten wijzigen vanuit de pop-up in de planning.',
          '#82: Datum van een dag wijzigen met behoud van ingeplande medewerkers.',
          '#99: Lader en/of contactpersoon bouwplaats wordt nu aangepast bij het verwijderen van de medewerker.',
        ],
      ],
      [
        'title' => 'Quick fixes na eerste release / overleg',
        'version' => '0.1.3',
        'date' => '2025-05-02',
        'changes' => [
          '#100: Bewerken van project: scherm aangepast, datums zijn makkelijk verwijderbaar, en tijden kunnen gekopieerd worden.',
          '#98: Toon een melding als je niet-opgeslagen wijzigingen in de planning hebt en je sluit de pagina.',
        ],
      ],
      [
        'title' => 'Quick fixes na eerste release',
        'version' => '0.1.2',
        'date' => '2025-05-01',
        'changes' => [
          '#96: Bewerken van project verwijdert gekoppelde medewerkers.',
          '#95: Fix voor het ophalen van de beschikbaarheid van medewerkers.',
        ],
      ],
      [
        'title' => 'Quick fixes na eerste release',
        'version' => '0.1.1',
        'date' => '2025-05-01',
        'changes' => [
          '#97: Changelog toegevoegd.',
          '#95: Indufast-events worden uitgesloten van de beschikbaarheid van de medewerkers.',
          '#96: Medewerkers-pagina verplaatst naar user menu.',
          '#87: Het kiezen van contactpersoon bouwplaats en lader in project-popup bij de planning.',
        ],
      ],
      [
        'title' => 'Eerste release!',
        'version' => '0.1',
        'date' => '2023-04-29',
        'changes' => [
          'Medewerkers en projecten beheren.',
          'Beschikbaarheid medewerkers ophalen.',
          'Medewerkers aan projecten koppelen.',
        ],
      ],
    ];

    public function executeChangelog(): void {
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $this->changeLog);
    }

  }