<?php

  use classes\ApiResponse;
  use Somnambulist\Components\Validation\Factory;

  trait IndufastEmployeeApiTrait {

    public function executeEmployeeCreate(): void {
      $employee = new IndufastEmployee();
      $employee->fill((array)$this->data)->validate();

      try {
        $employee->save();
        ApiResponse::sendResponseOK('Employee saved', $employee);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.');
      }
    }

    public function executeEmployeeList(): void {
      $rules = [
        'active' => 'string|in:true,false',
        'sort' => 'string|in:name,rank',
      ];
      $data = $this->validateRequest($_GET, $rules)->getValidatedData();

      $conditions = [];
      if ($data['active']) {
        $conditions['active'] = $data['active'] == 'true';
      }

      $sort = match ($data['sort'] ?? null) {
        'rank' => 'ORDER BY rank,rank_number ASC',
        default => 'ORDER BY name ASC',
      };

      $employees = IndufastEmployee::find_all_by($conditions, $sort);

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $employees);
    }

    public function executeEmployeeUpdate(): void {
      if (!$employee = IndufastEmployee::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Employee not found');
      }

      $employee->fill((array)$this->data)->validate();

      try {
        $employee->save();
        ApiResponse::sendResponseOK('Employee saved', $employee);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.', (DEVELOPMENT) ? $e->getMessage() : '');
      }
    }

    public function executeEmployeeDelete(): void {
      if (!$employee = IndufastEmployee::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Employee not found');
      }

      try {
        $employee->destroy();
        ApiResponse::sendResponseOK('Employee deleted', $employee);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while deleting.');
      }
    }

  }