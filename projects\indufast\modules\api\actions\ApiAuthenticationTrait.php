<?php

  use classes\ApiResponse;

  trait ApiAuthenticationTrait {

    protected function authenticate(): void {
      if (empty($_SESSION['loggedIn']) || empty($_SESSION['userObject'])) {
        ApiResponse::sendResponseUnauthorized();
      }
    }

    public function executeLogin(): void {
      $requestData = $this->data;

      $loginEmail = trim($requestData->email ?? '');
      $loginPassword = trim($requestData->password ?? '');
      $loginResult = $this->loginByEmail($loginEmail, $loginPassword, Site::getLoginUsergroups());

      if (is_a($loginResult, User::class)) {
        $user = (object)[ // only return needed fields
          'firstName' => $loginResult->firstname,
          'insertion' => $loginResult->insertion,
          'lastName'  => $loginResult->lastname,
        ];
        ApiResponse::sendResponseOK('Je bent succesvol ingelogd', ['user' => $user]);
      }

      ApiResponse::sendResponseError($loginResult[0]);
    }

    public function executeLogout(): void {
      GsdSession::stopSession(true);
      ApiResponse::sendResponseOK('Je bent succesvol uitgelogd');
    }

  }