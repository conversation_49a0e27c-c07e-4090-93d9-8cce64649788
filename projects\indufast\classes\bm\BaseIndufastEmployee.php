<?php
class BaseIndufastEmployee extends AppModel {

  const DB_NAME = '';
  const TABLE_NAME = 'indufast_employee';
  const OM_CLASS_NAME = 'IndufastEmployee';
  const columns = ['id', 'name', 'type', 'email', 'extra_email', 'rank', 'rank_number', 'industries', 'private', 'team_lead', 'active', 'insertTS', 'updateTS'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'type'                        => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['staff','subcontractor']],
    'email'                       => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'extra_email'                 => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'rank'                        => ['type' => 'varchar', 'length' => '1', 'null' => false],
    'rank_number'                 => ['type' => 'int', 'length' => '11', 'null' => false],
    'industries'                  => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'private'                     => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'team_lead'                   => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'active'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'insertTS'                    => ['type' => 'datetime', 'length' => '', 'null' => false],
    'updateTS'                    => ['type' => 'datetime', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $name, $type, $email, $extra_email, $rank, $rank_number, $industries, $private, $team_lead, $active, $insertTS, $updateTS;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastEmployee[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastEmployee[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return IndufastEmployee[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return IndufastEmployee
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return IndufastEmployee
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}