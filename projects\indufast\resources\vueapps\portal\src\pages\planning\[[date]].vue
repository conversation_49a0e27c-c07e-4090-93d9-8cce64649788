<script setup>
import {computed, onMounted, onUnmounted, ref, watch} from "vue";
import ProjectDetails from "@/components/planning/ProjectDetails.vue";
import Projects from "@/components/planning/Projects.vue";
import MultiDay from "@/components/planning/MultiDay.vue";
import createApiService from "@/services/api.js";
import {onBeforeRouteLeave, useRoute, useRouter} from "vue-router";
import {definePage} from "unplugin-vue-router/runtime";
import {format} from "date-fns";
import {industryTypes, employeeAvailability} from "@/helpers/constants.js";
import {useSnackbarStore} from "@/stores/snackbar.js";
import EmployeeDetails from "@/components/planning/EmployeeDetails.vue";

const router = useRouter();
const route = useRoute();
const loading = ref({
  projects: true,
  employees: true,
  save_all: false,
});
const employees = ref([]);
const displayedEmployee = ref();
const showEmployeeDialog = ref(false);
const api = createApiService(router);
const projects = ref([]);
const draggedEventEmployee = ref({});
const sourceCalendarEvent = ref({});
const snackbarStore = useSnackbarStore();
const changedCalendarEvents = ref([]);
const date = ref(route.params.date?.length ? route.params.date : format(new Date(), 'yyyy-MM-dd'));
const availabilityInterval = setInterval(() => {
  updateAvailability(false);
}, 30_000);
const multiDayDialog = ref(false);
const multiDayProject = ref({});
const multiDayTargetEvent = ref({});
const multiDayEmployee = ref({});

definePage({
  name: "/planning",
  meta: {
    title: 'Planning',
  },
})

onMounted(() => {
  updateEmployees()
  updateProjects()
});

onBeforeRouteLeave((to, from, next) => {
  if (hasUnsavedChanges.value) {
    if (confirm('Er zijn nog wijzigingen die niet zijn opgeslagen. Weet je zeker dat je de pagina wil verlaten?')) {
      next();
    }
  }
  else {
    next();
  }
});

onUnmounted(() => {
  if (availabilityInterval) {
    clearInterval(availabilityInterval);
  }

  window.removeEventListener("beforeunload", preventClose);
});

const preventClose = (event) => {
  if (changedCalendarEvents.value.length) {
    event.preventDefault();
    event.returnValue = '';
  }
};

window.addEventListener('beforeunload', preventClose);

const updateEmployees = () => {
  api
    .get("employeeList?" + new URLSearchParams({
      active: true,
      sort: 'rank',
    }).toString())
    .then((response) => {
      employees.value = response.data.data;
      loading.value.employees = false;
      updateAvailability();
    })
    .catch((error) => {
      loading.value.employees = false;
      snackbarStore.showMessage("Onbekende fout bij het ophalen van de medewerkers.", "error");
      console.log(error);
    });
}

const updateAvailability = (showLoading = true) => {
  if (showLoading) {
    loading.value.employees = true;
    for (let i in employees.value) {
      employees.value[i]._availability = 'unknown';
    }
  }

  api
    .get("employeeAvailabilityList?" + new URLSearchParams({
      date: date.value,
    }).toString())
    .then((response) => {
      for (let i in response.data.data) {
        const employee = employees.value.find((employee) => employee.id === response.data.data[i].employee.id);
        if (employee) {
          employee._availability = response.data.data[i].availability;
          employee._events = response.data.data[i].events;
        }
      }
      loading.value.employees = false;
      detectAllConflicts();
    })
    .catch((error) => {
      loading.value.employees = false;
      snackbarStore.showMessage("Onbekende fout bij het ophalen van de beschikbaarheid.", "error");
      console.log(error);
    });
}

const setEmployeeConflict = (employee, calendarEvent, conflictEvent) => {
  // Vind de betreffende eventEmployee in de conflicterende event
  const eventEmployee = calendarEvent.employees.find(e => e.employee.id === employee.id);

  if (eventEmployee) {
    eventEmployee.has_conflicts = true;

    const project = findProjectByEvent(calendarEvent.id);
    if (project) {
      project.is_plannable = false;
    }
  }

  if (!conflictEvent) return;

  // Als we ook de conflicterende event weten, markeer die eventEmployee ook
  const conflictEventEmployee = conflictEvent.employees.find(e => e.employee.id === employee.id);

  if (conflictEventEmployee) {
    conflictEventEmployee.has_conflicts = true;

    const project = findProjectByEvent(conflictEvent.id);
    if (project) {
      project.is_plannable = false;
    }
  }
};

const updateInternalAvailability = (employee, detectConflicts = false) => {
  if (employee._availability === 'not_available' || employee._availability === 'unknown') {
    return employee._availability;
  }

  let available_morning = employee._availability !== 'available_afternoon';
  let available_afternoon = employee._availability !== 'available_morning';

  const allEvents = filterEvents(projects.value.flatMap(project => project.events));

  let calendarEvents = allEvents.filter(event => event.employees.some(e => e.employee.id === employee.id))
  let morningEvent = null;
  let afternoonEvent = null;
  let wholeDayEvent = null;

  if (calendarEvents.length) {
    for (let j in calendarEvents) {
      const currentEvent = calendarEvents[j];

      // Skip current dragged event when calculating availability
      if (Object.keys(draggedEventEmployee.value).length &&
        employee.id === draggedEventEmployee.value.employee.id &&
        sourceCalendarEvent.value &&
        sourceCalendarEvent.value.id === currentEvent.id) {
        continue;
      }

      switch (calendarEvents[j].day_part) {
        case 'morning':
          if (!available_morning && detectConflicts) {
            setEmployeeConflict(employee, currentEvent, morningEvent || wholeDayEvent);
          }
          available_morning = false;
          morningEvent = currentEvent;
          break;

        case 'afternoon':
          if (!available_afternoon && detectConflicts) {
            setEmployeeConflict(employee, currentEvent, afternoonEvent || wholeDayEvent);
          }
          available_afternoon = false;
          afternoonEvent = currentEvent;
          break;

        case 'whole_day':
          if ((!available_morning || !available_afternoon) && detectConflicts) {
            setEmployeeConflict(employee, currentEvent, morningEvent || afternoonEvent || wholeDayEvent);
          }
          available_morning = false;
          available_afternoon = false;
          wholeDayEvent = currentEvent;
          break;
      }
    }

    if (!available_morning && !available_afternoon) {
      return 'not_available';
    }
    else if (available_morning && available_afternoon) {
      return 'available';
    }
    else if (available_morning) {
      return 'available_morning';
    }
    else if (available_afternoon) {
      return 'available_afternoon';
    }
  }

  return employee._availability;
}

const updateProjects = () => {
  if (!date.value) {
    return;
  }

  if (hasUnsavedChanges.value) {
    if (!confirm('Er zijn nog wijzigingen die niet zijn opgeslagen. Weet je zeker dat je de projecten wil verversen?')) {
      return;
    }
  }

  loading.value.projects = true;
  api
    .get("projectList?" + new URLSearchParams({
      archive: false,
      date: date.value,
    }).toString())
    .then((response) => {
      loading.value.projects = false;
      projects.value = response.data.data;
      changedCalendarEvents.value = [];
      detectAllConflicts();
    })
    .catch((error) => {
      loading.value = false;
      console.log(error);
    });
}

const filterEvents = (events) => {
  return events.filter((event) => {
    return event.type === 'work' && format(event.start, 'yyyy-MM-dd') === date.value;
  });
};

const calendarEventHasEmployee = (calendarEvent, employee) => {
  return calendarEvent.employees.some((w) => w.employee.id === employee.id);
};

const sortEmployeesByRank = (calendarEvent) => {
  calendarEvent.employees.sort((a, b) => {

    // Compare ranks alphabetically.
    if (a.employee.rank !== b.employee.rank) {
      return a.employee.rank.localeCompare(b.employee.rank);
    }

    // If ranks are the same, compare rank numbers numerically.
    return a.employee.rank_number - b.employee.rank_number;
  });
};

const onDragStart = (eventEmployee, calendarEvent, event) => {
  draggedEventEmployee.value = eventEmployee;
  sourceCalendarEvent.value = calendarEvent;
  event.target.classList.add("dragging");
};

const onDragEnd = (event) => {
  draggedEventEmployee.value = {};
  sourceCalendarEvent.value = {};
  event.target.classList.remove("dragging");
};

const checkAvailability = (targetCalendarEvent, employee, showMessages = false) => {
  let availability = updateInternalAvailability(employee, true);
  let result;

  if (calendarEventHasEmployee(targetCalendarEvent, employee)) {
    if (showMessages && sourceCalendarEvent.value.id !== targetCalendarEvent.id) {
      snackbarStore.showMessage(employee.name + " is al aan dit project gekoppeld.", "error");
    }
    return false;
  }

  switch (availability) {
    case 'available':
      result = true;
      break;

    case 'available_morning':
      result = targetCalendarEvent.day_part === 'morning';
      break;

    case 'available_afternoon':
      result = targetCalendarEvent.day_part === 'afternoon';
      break;

    case 'available_morning_afternoon':
      result = targetCalendarEvent.day_part === 'morning' || targetCalendarEvent.day_part === 'afternoon'
      break;

    case 'not_available':
      result = false;
      break;

    default:
      result = true;
  }

  if (showMessages) {
    if (result) {
      snackbarStore.showMessage(employee.name + " is aan dit project gekoppeld.", (availability !== "not_available_error") ? "success" : "warning");
    }
    else {
      snackbarStore.showMessage(employee.name + " is niet beschikbaar.", "error");
    }
  }

  return result;
};

const onDragEnter = (calendarEvent, event) => {
  if (calendarEvent.id !== sourceCalendarEvent.value.id) {
    event.currentTarget.classList.add((checkAvailability(calendarEvent, draggedEventEmployee.value.employee)) ? "drag-over-highlight" : "drag-over-invalid");
  }

  event.preventDefault();
};

const onDragLeave = (event) => {
  event.currentTarget.classList.remove("drag-over-highlight");
  event.currentTarget.classList.remove("drag-over-invalid");
};

const removeEmployeeFromEvent = (calendarEvent, eventEmployeeIndex) => {
  const removedEmployee = calendarEvent.employees.splice(eventEmployeeIndex, 1)[0];

  const project = findProjectByEvent(calendarEvent.id);
  if (project) {
    handleEmployeeRemovalFromProject(project, removedEmployee);
  }

  markCalendarEventAsChanged(calendarEvent);
  snackbarStore.showMessage(removedEmployee.employee.name + " is verwijderd van het project.", "success");

  detectAllConflicts();
};

const findProjectByEvent = (eventId) => {
  return projects.value.find((project) =>
    project.events.some((event) => event.id === eventId)
  );
};

const handleEmployeeRemovalFromProject = (project, removedEmployee) => {
  const isStillInProject = project.events.some((event) =>
    event.employees.some((employee) => employee.employee.id === removedEmployee.employee.id)
  );
  if (isStillInProject) return;

  // Remove the employee from any event where they were team lead or material loader
  project.events.forEach(event => {
    if (event.team_lead_employee_id === removedEmployee.employee.id) {
      event.team_lead_employee_id = null;
      event.team_lead = null;
    }
    if (event.material_load_employee_ids?.includes(removedEmployee.employee.id)) {
      event.material_load_employee_ids = event.material_load_employee_ids.filter(id => id !== removedEmployee.employee.id);
      event.material_loaders = event.material_loaders?.filter(loader => loader.id !== removedEmployee.employee.id) || [];
    }
  });
};

const onDrop = (targetCalendarEvent, event) => {
  onDragLeave(event);

  if (checkAvailability(targetCalendarEvent, draggedEventEmployee.value.employee, true)) {
    draggedEventEmployee.value.calendar_event_id = targetCalendarEvent.id;
    draggedEventEmployee.value.employee_id = draggedEventEmployee.value.employee.id;
    draggedEventEmployee.value.has_conflicts = false;
    targetCalendarEvent.employees.push(draggedEventEmployee.value);
    sortEmployeesByRank(targetCalendarEvent);

    markCalendarEventAsChanged(targetCalendarEvent);

    if (Object.keys(sourceCalendarEvent.value).length) {
      markCalendarEventAsChanged(sourceCalendarEvent.value);

      const index = sourceCalendarEvent.value.employees.findIndex(
        (w) => w.employee.id === draggedEventEmployee.value.employee_id
      );
      if (index !== -1) {
        const removedEmployee = sourceCalendarEvent.value.employees.splice(index, 1)[0];

        // Handle employee removal from the source project
        const sourceProject = findProjectByEvent(sourceCalendarEvent.value.id);
        if (sourceProject) {
          handleEmployeeRemovalFromProject(sourceProject, removedEmployee);
        }
      }
    }
    detectAllConflicts();

    // Check if the target project has multiple work events.
    const targetProject = projects.value.find(project =>
      project.events.some(event => event.id === targetCalendarEvent.id)
    );
    if (targetProject && targetProject.events.filter(event => event.type === 'work').length > 1) {
      multiDayDialog.value = true;
      multiDayProject.value = targetProject;
      multiDayEmployee.value = draggedEventEmployee.value.employee;
      multiDayTargetEvent.value = targetCalendarEvent;
    }
  }
};

const hideMultiDayDialog = () => {
  multiDayDialog.value = false;
};

const saveMultiDayChanges = (events) => {
  events.forEach((event) => {
    const targetEvent = projects.value
      .flatMap(project => project.events)
      .find(e => e.id === event.id);

    if (targetEvent) {
      targetEvent.employees.push({
        employee: multiDayEmployee.value,
        calendar_event_id: event.id,
        employee_id: multiDayEmployee.value.id,
      });

      markCalendarEventAsChanged(targetEvent);
    }
  });

  hideMultiDayDialog();
};

const detectAllConflicts = () => {
  // Reset alle conflict statussen eerst
  projects.value.forEach(project => {
    project.is_plannable = true;
    project.events.forEach(event => {
      event.employees.forEach(eventEmployee => {
        eventEmployee.has_conflicts = false;
      });
    });
  });

  // Loop door alle medewerkers en controleer conflicten
  employees.value.forEach(employee => {
    updateInternalAvailability(employee, true);
  });
};

const markCalendarEventAsChanged = (calendarEvent) => {
  if (!changedCalendarEvents.value.some((event) => event.id === calendarEvent.id)) {
    changedCalendarEvents.value.push(calendarEvent);
  }
};

const saveAllChanges = async () => {
  loading.value.save_all = true;

  const changedCalendarEventsCopy = [...changedCalendarEvents.value];
  try {
    const eventPromises = changedCalendarEventsCopy.map((event) => {
      const data = {
        employee_ids: event.employees.map((employee) => employee.employee.id),
        team_lead_employee_id: event.team_lead_employee_id,
        material_load_employee_ids: event.material_load_employee_ids || []
      };

      return api.post(`eventUpdate?id=${event.id}`, JSON.stringify(data))
        .then(() => {
          changedCalendarEvents.value = changedCalendarEvents.value.filter(
            (e) => e.id !== event.id
          );
        });
    });

    await Promise.all(eventPromises);

    loading.value.save_all = false;

    snackbarStore.showMessage("Alle gewijzigde projecten zijn opgeslagen.", "success");

    // Wait 5 seconds to make sure google calendar is updated
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Update employee availability after all changes are saved
    await api.put("employeeAvailabilityUpdate");

    loading.value.save_all = false;
  } catch (error) {
    snackbarStore.showMessage("Onbekende fout bij het opslaan van de gewijzigde projecten.", "error");
    loading.value.save_all = false;
    console.error(error);
  }
};

const projectIsChanged = (project) => {
  return project.events.some((event) => {
    return changedCalendarEvents.value.some((changedEvent) => {
      return event.id === changedEvent.id;
    });
  });
};

const showEmployeeDetails = (employee) => {
  displayedEmployee.value = employee;
  showEmployeeDialog.value = true;
};

const hasUnsavedChanges = computed(() => !!changedCalendarEvents.value.length);

watch(route, () => {
  if (route.params.date !== date.value) {
    if (hasUnsavedChanges.value) {
      if (!confirm('Er zijn nog wijzigingen die niet zijn opgeslagen. Weet je zeker dat je de pagina wil verlaten?')) {
        router.replace({ name: '/planning', params: { date: date.value } });
        return;
      }
    }

    date.value = route.params.date?.length ? route.params.date : format(new Date(), 'yyyy-MM-dd')
    changedCalendarEvents.value = [];
    updateProjects();
    updateAvailability();
  }
})

</script>

<template>
  <v-card class="flex-grow-1 d-flex flex-column">
    <v-card-title class="space-between pt-4 pb-4">
      Planning voor {{ $filters.formatDate(date) }}
      <v-btn
        prepend-icon="mdi-calendar"
        color="primary"
        :to="{ name: '/calendar', params: { date: date } }"
      >
        Kalender
      </v-btn>
    </v-card-title>
    <v-divider />
    <v-row class="pl-3 pr-3 flex-grow-1">
      <v-col
        cols="4"
        class="section paddingSection d-flex flex-column"
      >
        <v-card-title class="pl-0 pr-0">
          Medewerkers
          <v-icon
            :disabled="loading.employees"
            color="primary"
            size="small"
            @click="updateEmployees"
          >
            mdi-refresh
          </v-icon>
          <v-progress-linear
            :active="loading.employees"
            indeterminate
            color="primary"
          />
        </v-card-title>
        <div class="flex-1-0-0 overflow-y-auto">
          <v-card
            v-for="employee in employees"
            :key="employee.id"
            draggable="true"
            class="draggable pa-0 mb-3"
            border
            :ripple="false"
            @dragstart="(event) => { onDragStart({ employee: employee }, {}, event); }"
            @dragend="onDragEnd"
            @click="showEmployeeDetails(employee)"
          >
            <v-card-text class="employee-wrapper pa-2">
              <span class="employee-name">
                <v-icon
                  :color="employeeAvailability.find(type => type.value === updateInternalAvailability(employee))?.color"
                  :title="employeeAvailability.find(type => type.value === updateInternalAvailability(employee))?.title"
                >
                  {{ employeeAvailability.find(type => type.value === updateInternalAvailability(employee))?.icon }}
                </v-icon>
                {{ employee.name }}
              </span>
              <span class="employee-data">
                <v-icon
                  v-for="(industry, index) in employee.industries"
                  :key="index"
                  class="mr-1"
                  :color="industryTypes.find(type => type.value === industry)?.color"
                  :title="industryTypes.find(type => type.value === industry)?.title"
                >
                  {{ industryTypes.find(type => type.value === industry)?.icon }}
                </v-icon>
                {{ employee.rank }}{{ employee.rank_number }}
              </span>
            </v-card-text>
          </v-card>
        </div>
      </v-col>

      <v-col
        cols="4"
        class="section paddingSection background d-flex flex-column"
      >
        <v-card-title class="pl-0">
          Projecten
          <v-icon
            :disabled="loading.projects"
            color="primary"
            size="small"
            @click="updateProjects"
          >
            mdi-refresh
          </v-icon>
        </v-card-title>
        <v-progress-linear
          :active="loading.projects"
          indeterminate
          color="primary"
        />
        <div class="flex-1-0-0 overflow-y-auto">
          <Projects
            :projects="projects"
            :on-drag-enter="onDragEnter"
            :on-drag-leave="onDragLeave"
            :on-drop="onDrop"
            :dragged-event-employee="draggedEventEmployee"
            :filter-events="filterEvents"
            :project-is-changed="projectIsChanged"
            :mark-calendar-event-as-changed="markCalendarEventAsChanged"
            :has-unsaved-changes="hasUnsavedChanges"
            @saved="updateProjects"
          />
          <v-card-text
            v-if="!projects.length && !loading.projects"
            class="pa-0"
          >
            Geen projecten gevonden.
          </v-card-text>
        </div>
      </v-col>

      <v-col
        cols="4"
        class="section paddingSection d-flex flex-column"
      >
        <v-card-title class="pl-0">
          Details
        </v-card-title>
        <div class="flex-1-0-0 overflow-y-auto">
          <ProjectDetails
            :projects="projects"
            :on-drag-start="onDragStart"
            :on-drag-end="onDragEnd"
            :on-drag-enter="onDragEnter"
            :on-drag-leave="onDragLeave"
            :on-drop="onDrop"
            :dragged-event-employee="draggedEventEmployee"
            :source-calendar-event="sourceCalendarEvent"
            :remove-employee-from-event="removeEmployeeFromEvent"
            :filter-events="filterEvents"
          />
        </div>
      </v-col>
    </v-row>
  </v-card>

  <employee-details
    v-model="showEmployeeDialog"
    :employee="displayedEmployee"
    :update-internal-availability="updateInternalAvailability"
  />

  <MultiDay
    :show="multiDayDialog"
    :project="multiDayProject"
    :save-multi-day-changes="saveMultiDayChanges"
    :employee="multiDayEmployee"
    :target-event="multiDayTargetEvent"
    :hide-multi-day-dialog="hideMultiDayDialog"
  />

  <v-fab
    v-if="hasUnsavedChanges"
    size="large"
    color="primary"
    location="top right"
    prepend-icon="mdi-content-save"
    app
    :loading="loading.save_all"
    @click="saveAllChanges"
  >
    Sla wijzigingen op ({{ Object.keys(changedCalendarEvents).length }})
  </v-fab>
</template>


<style lang="scss">
.draggable {
  width: 100%;
  color: black;
  cursor: grab;
}

.paddingSection {
  padding-left: 1rem;
  padding-right: 1rem;
}

.fill-height {
  padding-left: 0;
  padding-right: 0;
}

.drag-over-highlight > * {
  background-color: rgba(var(--v-theme-indufastGreen), var(--v-light-opacity)) !important;
  transition: background-color 0.2s ease-in-out !important;
}

.drag-over-invalid > * {
  background-color: rgba(var(--v-theme-indufastRed), var(--v-light-opacity)) !important;
  transition: background-color 0.2s ease-in-out !important;
}

.drag-over-invalid *,
.drag-over-highlight * {
  color: rgb(var(--v-theme-on-surface)) !important;
}

.dragging {
  opacity: 0.5;
}

.employee-wrapper {
  display: flex !important;
  justify-content: space-between;
  background-color: rgb(var(--v-theme-GSDBackground));
}
</style>
