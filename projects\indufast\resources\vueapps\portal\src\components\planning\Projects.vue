<script setup>
import {defineProps, ref} from "vue";
import {projectStatus} from "@/helpers/constants.js";
import ProjectViewDialog from "@/components/project/ProjectViewDialog.vue";
import ProjectEditDialog from "@/components/project/ProjectEditDialog.vue";

const showViewProjectDialog = ref(false);
const showEditProjectDialog = ref(false);
const projectToView = ref(null);
const projectToEdit = ref(null);

const props = defineProps({
  projects: {
    type: Array,
    required: true,
  },
  projectIsChanged: {
    type: Function,
    required: true,
  },
  draggedEventEmployee: {
    type: Object,
    required: true,
  },
  hasUnsavedChanges: {
    type: Boolean,
    required: true,
  },
  onDrop: {
    type: Function,
    required: true,
  },
  onDragEnter: {
    type: Function,
    required: true,
  },
  onDragLeave: {
    type: Function,
    required: true,
  },
  filterEvents: {
    type: Function,
    required: true,
  },
  markCalendarEventAsChanged: {
    type: Function,
    required: true,
  },
});
const emit = defineEmits(["saved"]);

const openView = (project) => {
  projectToView.value = project;
  showViewProjectDialog.value = true;
};

const openEdit = (project) => {
  showViewProjectDialog.value = false;
  projectToEdit.value = project;
  showEditProjectDialog.value = true;
};

const getFte = (project, event) => {
  return event.fte || project.fte;
}
</script>

<template>
  <template
    v-for="project in projects"
    :key="project.id"
  >
    <v-card
      v-for="(calendarEvent, index) in filterEvents(project.events)"
      :key="index"
      droppable="true"
      :class="{
        dropzone: Object.keys(props.draggedEventEmployee).length !== 0,
        'pa-0 pl-0 mb-4 project': true,
        'not-plannable': !project.is_plannable,
      }"
      @dragenter="(event) => onDragEnter(calendarEvent, event)"
      @dragover.prevent
      @dragleave="(event) => onDragLeave(event)"
      @drop="(event) => onDrop(calendarEvent, event)"
      @click="openView(project)"
    >
      <v-toolbar
        class="ma-0 pa-1"
        height="25"
        :color="projectStatus.find(status => status.value === project.status)?.color"
      >
        <v-toolbar-title class="ml-1 projectTitle">
          <v-icon
            v-if="!project.is_plannable"
            title="Dit project heeft planningsconflicten"
            icon="mdi-alert-circle-outline"
            class="mr-1"
          />
          <v-icon
            v-if="getFte(project, calendarEvent) > calendarEvent.employees.length"
            title="Er zijn minder medewerkers ingepland dan het aantal FTE's van dit project"
            icon="mdi-account-plus"
            class="mr-1"
          />
          {{ project.name }}
        </v-toolbar-title>
        <v-toolbar-items class="mr-2">
          <v-icon
            v-if="projectIsChanged(project)"
            title="Project is gewijzigd"
            icon="mdi-content-save"
            class="mr-1"
          />
          <v-icon
            icon="mdi-account"
            class="mr-1"
          />
          {{ calendarEvent.employees.length }}
          <template v-if="getFte(project, calendarEvent)">
            / {{ getFte(project, calendarEvent) }}
          </template>
        </v-toolbar-items>
      </v-toolbar>
      <v-list
        density="compact"
        slim
      >
        <v-list-item prepend-icon="mdi-clock-outline">
          {{ $filters.formatTime(calendarEvent.start) }} - {{ $filters.formatTime(calendarEvent.end) }}
        </v-list-item>
        <v-list-item prepend-icon="mdi-map-marker">
          {{ project.address }}
        </v-list-item>
      </v-list>
    </v-card>
  </template>
  <project-view-dialog
    v-model="showViewProjectDialog"
    :project="projectToView"
    :project-is-changed="projectIsChanged"
    :has-unsaved-changes="hasUnsavedChanges"
    :mark-calendar-event-as-changed="markCalendarEventAsChanged"
    @edit="openEdit"
    @save="showViewProjectDialog = false"
  />
  <project-edit-dialog
    v-model="showEditProjectDialog"
    :project-data="projectToEdit"
    @saved="emit('saved')"
  />
</template>

<style scoped>
.dropzone {
  background-color: white;
}

.dropzone * {
  pointer-events: none;
}

.projectTitle {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 15px;
}

.project {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  box-sizing: border-box;
}

.not-plannable {
  border: 1.5px solid red !important;
}
</style>
