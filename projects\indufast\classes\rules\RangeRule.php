<?php declare(strict_types=1);

  namespace classes\rules;

  use Somnambulist\Components\Validation\Exceptions\ParameterException;
  use Somnambulist\Components\Validation\Rule;

  class RangeRule extends Rule {

    protected string $message = ":attribute invalid range";
    protected array $fillableParams = ['table', 'column'];

    public function fillParameters(array $params): self
    {
      $this->params['field']  = $params[0];
      $this->params['direction'] = $params[1] ?? 'asc';

      return $this;
    }

    /**
     * @throws ParameterException
     */
    public function check($value): bool {
      $this->assertHasRequiredParameters(['field', 'direction']);

      $anotherAttribute  = $this->parameter('field');
      $anotherValue      = $this->attribute()->value($anotherAttribute);
      $direction  = $this->parameter('direction');

      return ($direction == 'asc') ? ($anotherValue > $value) : ($anotherValue < $value);
    }

  }