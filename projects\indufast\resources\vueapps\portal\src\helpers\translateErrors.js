export const translateErrors = (errors, name, length = true) => {
  for (let i in errors) {
    let error = errors[i].match(/^(\S+)\s+(.*?)\s*(\d+)?$/)
    switch (error[2]) {
      case 'is required':
        errors[i] = name + ' is verplicht';
        break;
      case 'is not a valid email address':
        errors[i] = 'Ongeldig e-mailadres';
        break;
      case 'is not valid date format':
        errors[i] = 'Ongeldige datum of tijd';
        break;
      case 'maximum is':
        errors[i] = (length) ? 'Maximale lengte is ' + error[3] + ' karakters' : 'Maximale waarde is ' + error[3];
        break;
      case 'minimum is':
        errors[i] = (length) ? 'Minimale lengte is ' + error[3] + ' karakters' : 'Minimale waarde is ' + error[3];
        break;
      case 'must be unique':
        errors[i] = 'Waarde moet uniek zijn';
        break;
      case 'invalid range':
        errors[i] = 'Ongeldig tijdsbereik';
        break;
    }
  }

  return errors;
}
