<script setup>
import { reactive, ref, toRefs, watch } from "vue";
// eslint-disable-next-line no-unused-vars
import tinymce from "tinymce/tinymce";
import "tinymce/models/dom";
import "tinymce/skins/ui/oxide/skin.css";
import "tinymce/themes/silver";
import "tinymce/icons/default";
import "tinymce/plugins/lists";
import Editor from "@tinymce/tinymce-vue";

const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  plugins: {
    type: [String, Array],
    default: "lists",
  },
  toolbar: {
    type: [String, Array],
    default: "bold italic underline | numlist bullist | removeformat",
  },
  class: {
    type: String,
    default: "",
  },
  errorMessages: {
    type: Array,
    default: new Array(0),
  },
});

const emit = defineEmits(["update:modelValue"]);
const init = reactive({
  height: 300,
  menubar: false,
  content_css: false,
  skin: false,
  plugins: props.plugins,
  toolbar: props.toolbar,
  branding: false,
  selector: "textarea",
  formats: {
    bold: { inline: "b", exact: true },
    italic: { inline: "i", exact: true },
    underline: { inline: "u", exact: true },
  },
  statusbar: false,
  content_style:
  "body:hover { background-color: #ededed; } body:focus { background-color: #dbdbdb; } body { font-size: 16px; font-family: Poppins, Sans-serif; background-color: #f6f6f6; color: rgba(0, 0, 0, 0.87); }",
});
const { modelValue } = toRefs(props);
const editorValue = ref(modelValue.value);

watch(modelValue, (newValue) => {
  editorValue.value = newValue;
});

watch(editorValue, (newValue) => {
  emit("update:modelValue", newValue);
});
</script>

<template>
  <div :class="props.class">
    <Editor
      v-model="editorValue"
      :init="init"
      license-key="gpl"
    />
  </div>
  <v-textarea
    v-if="errorMessages.length"
    :error-messages="errorMessages"
    class="error-messages-only"
  />
</template>

<style>
.tox-tinymce {
  border-radius: 5px 5px 0 0;
  border: 1px solid rgb(221, 221, 221);
}

.tox .tox-edit-area::before {
  border: none !important;
}

.error-messages-only textarea {
  display: none;
}
</style>
