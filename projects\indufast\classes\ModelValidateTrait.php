<?php

  use classes\ApiResponse;
  use classes\rules\ExistsRule;
  use classes\rules\RangeRule;
  use classes\rules\UniqueRule;
  use Somnambulist\Components\Validation\ErrorBag;
  use Somnambulist\Components\Validation\Factory;

  trait ModelValidateTrait {

    protected function setId($fillable): array {
      return array_map(function($item) {
        if (is_string($item)) {
          return str_replace('{id}', $this->id ?? '', $item);
        } elseif (is_array($item)) {
          return array_map(fn($v) => is_string($v) ? str_replace('{id}', $this->id ?? '', $v) : $v, $item);
        }
        return $item;
      }, $fillable);
    }

    /**
     * Validate the model properties against the fillable field definitions.
     */
    public function validate(): void {
      $factory = new Factory();
      $factory->addRule('unique', new UniqueRule());
      $factory->addRule('unique2', new UniqueRule());
      $factory->addRule('exists', new ExistsRule());
      $factory->addRule('range', new RangeRule());
      $validation = $factory->make(get_object_vars($this), $this->setId($this->getFillable()));
      $validation->validate();

      if ($validation->fails()) {
        ApiResponse::sendResponseError('errors', $this->parseErrors($validation->errors()));
      }
    }

    public function getFillable(): array {
      return $this->fillable ?? [];
    }

    /**
     * Parse the errors from the validation into a keyed array.
     */
    public function parseErrors(ErrorBag $errors): array {
      $results = [];

      foreach (array_keys($this->getFillable()) as $key) {
        $results[$key] = array_values($errors->get($key));
      }

      return array_filter($results);
    }

  }

