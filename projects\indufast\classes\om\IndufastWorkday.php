<?php

  use classes\TimesTrait;

  AppModel::loadModelClass('IndufastWorkdayModel');

  class IndufastWorkday extends IndufastWorkdayModel {

    use PropertyCastTrait {
      castProperties as castPropertiesTrait;
    }
    use TimesTrait;

    /** @var WorkdayLines[] */
    public array $lines;
    public string $travelToStart = '';
    public string $travelToEnd = '';
    public string $travelToDuration = '';
    public string $travelToDurationNet = '';
    public string $workdayStart = '';
    public string $workdayEnd = '';
    public string $workdayDuration = '';
    public string $workdayPause = '';
    public string $workdayDurationNet = '';
    public string $workdayOutsideWorkingHours = '';
    public string $travelFromStart = '';
    public string $travelFromEnd = '';
    public string $travelFromDuration = '';
    public string $travelFromDurationNet = '';
    public string $travelDurationNet = '';

    const CAST_PROPERTIES = [
      'id'                        => 'int',
      'travel_to_end_line_id'     => 'int',
      'travel_from_start_line_id' => 'int',
      'user_id'                   => 'hidden',
      'from_db'                   => 'hidden',
      'insertTS'                  => 'hidden',
      'updateTS'                  => 'hidden',
    ];

    /**
      * @return IndufastWorkdayLine[]
      */
    public function lines(): array {
      return $this->lines ?? $this->lines = IndufastWorkdayLine::find_all_by(['workday_id' => $this->id], 'ORDER BY start');
    }

    public function castProperties(): void {
      $this->castPropertiesTrait();

      foreach ($this->lines() as $workdayLine) {
        $workdayLine->castProperties($this);
      }
    }

    public function setDefaults(): void {
      parent::setDefaults();
      $this->insertTS = date("Y-m-d H:i:s");
    }

    public function calculate(): void {
      if (!$this->travel_to_end_line_id && !$this->travel_from_start_line_id) {
        return;
      }

      $max = '00:00:00';
      foreach ($this->lines() as $line) {
        if ($line->void) {
          continue;
        }

        if ($line->id == $this->travel_from_start_line_id) {
          $this->travelFromStart = $line->start;
          $this->workdayEnd = $line->start;
        }

        if (!$this->travelToEnd && !$this->travelFromStart && $this->travel_to_end_line_id) {
          $line->type = 'travel-to';
          $max = max($line->end, $max);
          if (!$this->travelToStart) {
            $this->travelToStart = $line->start;
          }
        }
        elseif ($this->travelFromStart) {
          $line->type = 'travel-from';
          $this->travelFromEnd = max($line->end, $this->travelFromEnd);
          $this->travelFromDuration = $this->duration($this->travelFromStart, $this->travelFromEnd);
          $this->travelFromDurationNet = $this->duration($this->travelFromStart, $this->travelFromEnd, 45);
        }
        else {
          $line->type = null;
        }

        if ($line->id == $this->travel_to_end_line_id) {
          $this->travelToEnd = max($line->end, $max);
          $this->travelToDuration = $this->duration($this->travelToStart, $this->travelToEnd);
          $this->travelToDurationNet = $this->duration($this->travelToStart, $this->travelToEnd, 45);
          $this->workdayStart = $this->travelToEnd;
        }
      }

      // Calculate total travel time.
      if ($this->travelToStart && $this->travelToEnd) {
        $this->travelDurationNet = $this->addTimes([$this->travelToDurationNet, $this->travelFromDurationNet]);
      }

      // Calculate workday.
      if ($this->workdayStart && $this->workdayEnd) {
        $this->workdayDuration = $this->duration($this->workdayStart, $this->workdayEnd);
        $this->workdayPause = ($this->workdayDuration >= '10:00:00') ? 60 : 45;
        $this->workdayDurationNet = $this->duration($this->workdayStart, $this->workdayEnd, $this->workdayPause);
        $this->workdayOutsideWorkingHours = $this->addTimes([
          $this->duration($this->workdayStart, '6:00:00'),
          $this->duration('18:00:00', $this->workdayEnd),
        ]);
      }
      else {
        $this->workdayStart = '';
      }
    }

    /**
     * @return IndufastWorkday[]
     */
    public static function findAllByMonth(int $userId, int $year, int $month): array {
      return parent::find_all_by(['user_id' => $userId], sprintf('AND MONTH(date) = %d AND YEAR(date) = %d ORDER BY date', $month, $year));
    }
  }