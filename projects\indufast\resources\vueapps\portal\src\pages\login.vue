<script setup>
import {ref} from 'vue';
import {useRouter} from 'vue-router';
import {useAuthenticationStore} from '@/stores/authentication.js';
import {definePage} from "unplugin-vue-router/runtime";

import createApiService from '@/services/api';

const router = useRouter();
const api = createApiService(router);

definePage({
  meta: {
    title: 'Inloggen',
    requiresAuth: false,
    layout: 'unauthenticated',
  },
})

const loading = ref(false);
const email = ref('');
const password = ref('');
const loginErrors = ref([]);

const submitLogin = async () => {
  loading.value = true;
  resetErrors();

  try {
    const response = await api.post('login', {email: email.value, password: password.value});
    if (response.status === 200) {
      const authStore = useAuthenticationStore();
      authStore.setLoginData(response.data.data.user, true);

      await router.push('/');
    }
    else {
      loginErrors.value = ['Er is iets misgegaan. Probeer het later nog eens.'];
    }
  } catch (e) {
    loginErrors.value = e.response?.status === 400 ? e.response.data.message : ['Er is iets misgegaan. Probeer het later nog eens.'];
  } finally {
    loading.value = false;
  }
}

const resetErrors = () => {
  loginErrors.value = [];
}

</script>

<template>
  <v-card class="ma-auto mt-9 pa-5" max-width="500" :loading="loading" :disabled="loading" elevation="10">
    <img
      src="@/assets/logo_large.png"
      class="logo-large"
      alt="Indufast"
    >

    <v-form @submit.prevent="submitLogin">
      <v-card-text class="pl-0 pr-0">
        <v-alert v-if="loginErrors.length" class="mb-5" type="error" variant="tonal">
          <template #text>
            <span
              v-for="(error, index) in loginErrors"
              :key="index"
            >
              {{ error }}
            </span>
          </template>
        </v-alert>
        <v-text-field v-model="email" class="mb-2" label="E-mail" hide-details/>
        <v-text-field v-model="password" type="password" label="Wachtwoord" hide-details/>
      </v-card-text>

      <v-card-actions class="flex-column ma-auto pa-0 mt-2 actions">
        <v-btn text="Inloggen" variant="elevated" type="submit" color="primary"/>
      </v-card-actions>
    </v-form>
  </v-card>
</template>

<style scoped>
.logo-large {
  max-width: 100%;
}
.actions {
  min-height: auto;
}
</style>
