<script setup>
import {defineProps, ref, watch} from "vue";
import createApiService from "@/services/api.js";
import {useRouter} from "vue-router";
import {useSnackbarStore} from "@/stores/snackbar.js";
import {employeeAvailability} from "@/helpers/constants.js";

const router = useRouter();
const api = createApiService(router);
const loading = ref(true);
const availability = ref([]);
const changedEvents = ref([]);
const snackbarStore = useSnackbarStore();
const checkboxStates = ref({});

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  project: {
    type: Object,
    required: true,
  },
  targetEvent: {
    type: Object,
    required: true,
  },
  employee: {
    type: Object,
    required: true,
  },
  hideMultiDayDialog: {
    type: Function,
    required: true,
  },
  saveMultiDayChanges: {
    type: Function,
    required: true,
  },
});

const updateAvailability = () => {
  loading.value = true;
  availability.value = [];

  api
    .get("employeeAvailabilityForProject?" + new URLSearchParams({
      employee_id: props.employee.id,
      project_id: props.project.id,
      exclude_event_id: props.targetEvent.id,
    }).toString())
    .then((response) => {
      availability.value = response.data.data;
      loading.value = false;
    })
    .catch((error) => {
      loading.value = false;
      snackbarStore.showMessage("Onbekende fout bij het ophalen van de medewerkers.", "error");
      console.log(error);
    });
};

const getAvailability = (event) => {
  let unknown = {
    event: event,
    availability: 'unknown',
    conflict: false,
  }

  if (!availability.value.length) {
    return unknown;
  }

  let eventAvailability = availability.value.find((a) => a.event.id === event.id);
  return (eventAvailability) ? eventAvailability : unknown;
}

const getButtonProps = (event) => {
  let availability = getAvailability(event)

  // Find the event in the project data.
  let eventInProject = props.project.events.find(e => e.id === event.id);

  // Find if the employee is already in the event.
  let employeeInEvent = eventInProject.employees.some(e => e.employee_id === props.employee.id);

  // Initialize checkbox state if not exists
  if (checkboxStates.value[event.id] === undefined) {
    checkboxStates.value[event.id] = employeeInEvent;
  }

  return {
    disabled: employeeInEvent || availability.conflict,
    checked: checkboxStates.value[event.id],
    titleSuffix: (employeeInEvent) ? '(reeds ingepland)' : '',
    color: availability.conflict ? 'error' : employeeAvailability.find(type => type.value === getAvailability(event).availability)?.color
  };
}

const toggleEvent = (event, checked) => {
  checkboxStates.value[event.id] = checked;

  if (checked && !changedEvents.value.some(e => e.id === event.id)) {
    changedEvents.value.push(event);
  }
  else {
    changedEvents.value = changedEvents.value.filter(e => e.id !== event.id);
  }
}

watch (() => props.show, (newValue) => {
  if (newValue) {
    updateAvailability();
    changedEvents.value = [];
    checkboxStates.value = {};
  }
});

</script>

<template>
  <v-dialog
    width="700"
    scrollable
    :model-value="show"
    @update:model-value="hideMultiDayDialog"
  >
    <v-card>
      <v-toolbar color="primary">
        <v-toolbar-title>{{ employee.name }} ook op andere dagen inplannen</v-toolbar-title>
        <v-toolbar-items>
          <v-btn
            icon="mdi-close"
            @click="hideMultiDayDialog"
          />
        </v-toolbar-items>
      </v-toolbar>
      <v-card
        v-for="event in project.events?.filter(e => e.id !== targetEvent.id && e.type === 'work')"
        :key="event.id"
      >
        <span>
          <v-checkbox
            :loading="loading"
            :color="getButtonProps(event).color"
            :disabled="loading || getButtonProps(event).disabled"
            :model-value="getButtonProps(event).checked"
            hide-details
            class="pl-1"
            @update:model-value="(checked) => toggleEvent(event, checked)"
          >
            <template #label>
              <v-card-title class="pl-0">
                {{ $filters.ucFirst($filters.formatDate(event.start)) }}: {{ $filters.ucFirst($filters.formatTime(event.start)) }} - {{ $filters.ucFirst($filters.formatTime(event.end)) }} {{ getButtonProps(event).titleSuffix }}
              </v-card-title>
            </template>
          </v-checkbox>

          <v-card-text class="pt-0">
            Beschikbaarheid:
            <v-icon
              :color="employeeAvailability.find(type => type.value === getAvailability(event).availability)?.color"
              :title="employeeAvailability.find(type => type.value === getAvailability(event).availability)?.title"
            >
              {{ employeeAvailability.find(type => type.value === getAvailability(event).availability)?.icon }}
            </v-icon>
            {{ employeeAvailability.find(type => type.value === getAvailability(event).availability)?.title }}
          </v-card-text>
        </span>
      </v-card>
      <v-card-actions>
        <v-btn
          variant="elevated"
          color="primary"
          prepend-icon="mdi-content-save"
          :loading="loading"
          :disabled="!changedEvents.length"
          @click="saveMultiDayChanges(changedEvents)"
        >
          Opslaan
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
