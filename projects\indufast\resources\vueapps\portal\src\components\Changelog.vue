<script setup>

import createApiService from "@/services/api.js";
import {useRouter} from "vue-router";
import {computed, onMounted, ref, watch} from "vue";
import {version} from '@/../package';

const router = useRouter();
const api = createApiService(router);
const changelog = ref([]);
const showDialog = ref(false);

onMounted(() => {
  updateChangelog()

  setInterval(() => {
    updateChangelog();
  }, 300_000);
});

const updateAvailable = computed(() => {
  return changelog.value.length > 0 && changelog.value[0].version !== version;
});

const updateChangelog = () => {
  api
    .get("changelog")
    .then((response) => {
      changelog.value = response.data.data;
    })
    .catch((error) => {
      console.log(error);
    });
}

watch (() => showDialog.value, (newValue) => {
  if (newValue) {
    updateChangelog();
  }
});
</script>

<template>
  <v-btn
    icon="mdi-new-box"
    :color="updateAvailable ? 'indufastRed' : 'primary'"
    :variant="updateAvailable ? 'outlined' : 'text'"
    @click="showDialog = true"
  />
  <v-dialog
    v-model="showDialog"
    width="800"
    max-height="75%"
    scrollable
  >
    <v-toolbar color="primary">
      <v-toolbar-title>Changelog</v-toolbar-title>
      <v-toolbar-items>
        <v-btn
          icon="mdi-close"
          @click="showDialog = false"
        />
      </v-toolbar-items>
    </v-toolbar>
    <v-card>
      <v-alert
        v-if="updateAvailable"
        color="indufastRed"
      >
        Herlaad de pagina om versie {{ changelog[0].version }} te gebruiken. Huidige versie: {{ version }}.
      </v-alert>
      <v-alert
        v-else
      >
        Versie in gebruik: v{{ version }}.
      </v-alert>
      <v-card-text>
        <v-card
          v-for="(item, index) in changelog"
          :key="index"
        >
          <v-card-title>v{{ item.version }}: {{ item.title }}</v-card-title>
          <v-card-subtitle>{{ $filters.ucFirst($filters.formatDate(item.date)) }}</v-card-subtitle>
          <v-card-text>
            <ul
              v-for="(change, changeIndex) in item.changes"
              :key="changeIndex"
              class="changes"
            >
              <li>{{ change }}</li>
            </ul>
          </v-card-text>
        </v-card>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<style>
.changes {
  padding-left: 20px;
}
</style>
