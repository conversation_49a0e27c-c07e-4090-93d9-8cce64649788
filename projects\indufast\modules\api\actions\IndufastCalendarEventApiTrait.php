<?php

  use classes\ApiResponse;

  trait IndufastCalendarEventApiTrait {

    public function executeEventCreate(): void {
      $event = new IndufastCalendarEvent();
      $event->fill((array)$this->data)->validate();

      try {
        if (!is_null($this->data->employee_ids ?? null)) {
          $event->updateEmployees();
        }
        $event->save();

        ApiResponse::sendResponseOK('Event saved', $event);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.');
      }
    }

    public function executeEventUpdate(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Event not found');
      }
      $event->fill((array)$this->data)->validate();

      try {
        if (!is_null($this->data->employee_ids ?? null)) {
          $event->updateEmployees();
        }
        $event->save();
        ApiResponse::sendResponseOK('Event saved', $event);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.', (DEVELOPMENT) ? $e->getMessage() : '');
      }
    }

    public function executeEventList(): void {
      $rules = [
        'date' => 'required|date:Y-m-d',
        'view' => 'required|string|in:week,month',
      ];
      $validation = $this->validateRequest($_GET, $rules);
      $data = $validation->getValidatedData();

      if ($data['view'] == 'week') {
        $start = date('Y-m-d', strtotime('monday this week', strtotime($data['date'])));
        $end = date('Y-m-d', strtotime('sunday this week', strtotime($data['date'])));
      }
      else {
        $start = date('Y-m-01', strtotime($data['date']));
        $end = date('Y-m-t', strtotime($data['date']));

        // If start is not a monday, we have to adjust the start date to the first monday before start.
        if (date('N', strtotime($start)) != 1) {
          $start = date('Y-m-d', strtotime('last monday', strtotime($start)));
        }

        // If end is not a sunday, we have to adjust the end date to the first sunday after end.
        if (date('N', strtotime($end)) != 7) {
          $end = date('Y-m-d', strtotime('next sunday', strtotime($end)));
        }
      }

      $events = IndufastCalendarEvent::list($start, $end);
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $events);
    }


    public function executeEventDelete(): void {
      if (!$event = IndufastCalendarEvent::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Event not found');
      }

      try {
        $event->destroy();
        ApiResponse::sendResponseOK('Event deleted', $event);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while deleting.');
      }
    }

  }