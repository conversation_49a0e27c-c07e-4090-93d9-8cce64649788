<script setup>
import {definePage} from "unplugin-vue-router/runtime";

definePage({
  meta: {
    title: '404 - Page not found',
    requiresAuth: false,
    layout: 'unauthenticated',
  },
})
</script>

<template>
  <v-card
    class="ma-auto mt-9 pa-5"
    max-width="500"
    elevation="10"
  >
    <v-card-title>404: Page not found</v-card-title>
    <v-card-actions>
      <v-btn
        prepend-icon="mdi-home"
        color="primary"
        text="Home"
        variant="elevated"
        @click="$router.push('/')"
      />
    </v-card-actions>
  </v-card>
</template>
